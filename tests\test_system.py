"""
اختبارات النظام وإضافة بيانات تجريبية
"""

import sys
import os
from datetime import date, datetime
from decimal import Decimal

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.schema import DatabaseSchema
from database.repositories import *
from business.sales_manager import SalesManager
from business.purchase_manager import PurchaseManager
from business.inventory_manager import InventoryManager
from business.currency_manager import CurrencyManager
from business.accounting_manager import AccountingManager


class SystemTester:
    """فئة اختبار النظام"""
    
    def __init__(self):
        """تهيئة الاختبار"""
        # استخدام قاعدة بيانات في الذاكرة للاختبار
        self.db_schema = DatabaseSchema(":memory:")
        self.connection = self.db_schema.connect()
        
        # تهيئة المدراء
        self.sales_manager = SalesManager(self.connection)
        self.purchase_manager = PurchaseManager(self.connection)
        self.inventory_manager = InventoryManager(self.connection)
        self.currency_manager = CurrencyManager(self.connection)
        self.accounting_manager = AccountingManager(self.connection)
        
        # تهيئة المستودعات
        self.customer_repo = CustomerRepository(self.connection)
        self.supplier_repo = SupplierRepository(self.connection)
        self.diamond_repo = DiamondRepository(self.connection)
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        print("إعداد قاعدة البيانات...")
        self.db_schema.create_tables()
        self.db_schema.insert_default_data()
        print("✓ تم إعداد قاعدة البيانات")
    
    def add_sample_data(self):
        """إضافة بيانات تجريبية"""
        print("\nإضافة بيانات تجريبية...")
        
        # إضافة عملاء
        customers_data = [
            {
                'Name': 'أحمد محمد',
                'ContactInfo': 'أحمد محمد - عميل مميز',
                'Address': 'الرياض، المملكة العربية السعودية',
                'Email': '<EMAIL>',
                'Phone': '+966501234567',
                'IsActive': True,
                'CreatedAt': datetime.now()
            },
            {
                'Name': 'فاطمة علي',
                'ContactInfo': 'فاطمة علي - عميل جديد',
                'Address': 'جدة، المملكة العربية السعودية',
                'Email': '<EMAIL>',
                'Phone': '+966507654321',
                'IsActive': True,
                'CreatedAt': datetime.now()
            }
        ]
        
        customer_ids = []
        for customer_data in customers_data:
            customer_id = self.customer_repo.insert(customer_data)
            customer_ids.append(customer_id)
        
        print(f"✓ تم إضافة {len(customer_ids)} عميل")
        
        # إضافة موردين
        suppliers_data = [
            {
                'Name': 'شركة الألماس الدولية',
                'ContactInfo': 'مورد رئيسي للألماس',
                'Address': 'دبي، الإمارات العربية المتحدة',
                'Email': '<EMAIL>',
                'Phone': '+971501234567',
                'IsActive': True,
                'CreatedAt': datetime.now()
            },
            {
                'Name': 'مؤسسة الجواهر الثمينة',
                'ContactInfo': 'مورد محلي للألماس',
                'Address': 'الرياض، المملكة العربية السعودية',
                'Email': '<EMAIL>',
                'Phone': '+966112345678',
                'IsActive': True,
                'CreatedAt': datetime.now()
            }
        ]
        
        supplier_ids = []
        for supplier_data in suppliers_data:
            supplier_id = self.supplier_repo.insert(supplier_data)
            supplier_ids.append(supplier_id)
        
        print(f"✓ تم إضافة {len(supplier_ids)} مورد")
        
        # إضافة أسعار صرف
        usd_currency = self.currency_manager.get_currency_by_code('USD')
        sar_currency = self.currency_manager.get_currency_by_code('SAR')
        
        if usd_currency and sar_currency:
            # سعر الدولار مقابل الريال
            self.currency_manager.add_exchange_rate(
                usd_currency['CurrencyID'],
                sar_currency['CurrencyID'],
                Decimal('3.75'),
                date.today()
            )
            print("✓ تم إضافة أسعار الصرف")
        
        # إضافة ألماس
        diamonds_data = [
            {
                'Carat': Decimal('1.5'),
                'Color': 'D',
                'Clarity': 'VVS1',
                'Cut': 'Excellent',
                'Shape': 'Round',
                'CertificateNumber': 'GIA123456',
                'CostPrice': Decimal('5000'),
                'SellingPrice': Decimal('7500'),
                'CurrencyID': usd_currency['CurrencyID'] if usd_currency else 1,
                'Description': 'ألماس دائري ممتاز',
                'IsActive': True,
                'CreatedAt': datetime.now()
            },
            {
                'Carat': Decimal('2.0'),
                'Color': 'E',
                'Clarity': 'VS1',
                'Cut': 'Very Good',
                'Shape': 'Princess',
                'CertificateNumber': 'GIA789012',
                'CostPrice': Decimal('8000'),
                'SellingPrice': Decimal('12000'),
                'CurrencyID': usd_currency['CurrencyID'] if usd_currency else 1,
                'Description': 'ألماس أميرة فاخر',
                'IsActive': True,
                'CreatedAt': datetime.now()
            },
            {
                'Carat': Decimal('0.75'),
                'Color': 'F',
                'Clarity': 'VS2',
                'Cut': 'Good',
                'Shape': 'Oval',
                'CertificateNumber': 'GIA345678',
                'CostPrice': Decimal('2000'),
                'SellingPrice': Decimal('3000'),
                'CurrencyID': usd_currency['CurrencyID'] if usd_currency else 1,
                'Description': 'ألماس بيضاوي أنيق',
                'IsActive': True,
                'CreatedAt': datetime.now()
            }
        ]
        
        diamond_ids = []
        for diamond_data in diamonds_data:
            diamond_id = self.diamond_repo.insert(diamond_data)
            diamond_ids.append(diamond_id)
            
            # إضافة مخزون للألماس
            inventory_data = {
                'DiamondID': diamond_id,
                'Quantity': 10,  # كمية ابتدائية
                'ReservedQuantity': 0,
                'LastUpdated': datetime.now()
            }
            inventory_repo = InventoryRepository(self.connection)
            inventory_repo.insert(inventory_data)
        
        print(f"✓ تم إضافة {len(diamond_ids)} قطعة ألماس مع المخزون")
        
        return {
            'customer_ids': customer_ids,
            'supplier_ids': supplier_ids,
            'diamond_ids': diamond_ids
        }
    
    def test_sales_process(self, customer_ids, diamond_ids):
        """اختبار عملية المبيعات"""
        print("\nاختبار عملية المبيعات...")
        
        try:
            # إنشاء مبيعات جديدة
            sale_id = self.sales_manager.create_sale(
                customer_id=customer_ids[0],
                sale_date=date.today(),
                currency_id=1,  # USD
                notes="مبيعات تجريبية"
            )
            print(f"✓ تم إنشاء مبيعات رقم {sale_id}")
            
            # إضافة عناصر للمبيعات
            success1 = self.sales_manager.add_sale_item(
                sale_id=sale_id,
                diamond_id=diamond_ids[0],
                quantity=2,
                unit_price=Decimal('7500')
            )
            
            success2 = self.sales_manager.add_sale_item(
                sale_id=sale_id,
                diamond_id=diamond_ids[2],
                quantity=1,
                unit_price=Decimal('3000')
            )
            
            if success1 and success2:
                print("✓ تم إضافة عناصر المبيعات")
            
            # الحصول على تفاصيل المبيعات
            sale_details = self.sales_manager.get_sale_details(sale_id)
            if sale_details:
                print(f"✓ إجمالي المبيعات: {sale_details['total_amount']}")
            
            # إنهاء المبيعات
            finalized = self.sales_manager.finalize_sale(sale_id)
            if finalized:
                print("✓ تم إنهاء المبيعات وتحديث المخزون")
            
            return sale_id
            
        except Exception as e:
            print(f"✗ خطأ في اختبار المبيعات: {e}")
            return None
    
    def test_purchase_process(self, supplier_ids, diamond_ids):
        """اختبار عملية المشتريات"""
        print("\nاختبار عملية المشتريات...")
        
        try:
            # إنشاء مشتريات جديدة
            purchase_id = self.purchase_manager.create_purchase(
                supplier_id=supplier_ids[0],
                purchase_date=date.today(),
                currency_id=1,  # USD
                notes="مشتريات تجريبية"
            )
            print(f"✓ تم إنشاء مشتريات رقم {purchase_id}")
            
            # إضافة عناصر للمشتريات
            success = self.purchase_manager.add_purchase_item(
                purchase_id=purchase_id,
                diamond_id=diamond_ids[1],
                quantity=5,
                unit_price=Decimal('8000')
            )
            
            if success:
                print("✓ تم إضافة عناصر المشتريات")
            
            # الحصول على تفاصيل المشتريات
            purchase_details = self.purchase_manager.get_purchase_details(purchase_id)
            if purchase_details:
                print(f"✓ إجمالي المشتريات: {purchase_details['total_amount']}")
            
            # إنهاء المشتريات
            finalized = self.purchase_manager.finalize_purchase(purchase_id)
            if finalized:
                print("✓ تم إنهاء المشتريات وتحديث المخزون")
            
            return purchase_id
            
        except Exception as e:
            print(f"✗ خطأ في اختبار المشتريات: {e}")
            return None
    
    def test_inventory_management(self, diamond_ids):
        """اختبار إدارة المخزون"""
        print("\nاختبار إدارة المخزون...")
        
        try:
            # عرض المخزون المتاح
            available_inventory = self.inventory_manager.get_available_inventory()
            print(f"✓ عدد العناصر المتاحة في المخزون: {len(available_inventory)}")
            
            # البحث عن ألماس
            search_criteria = {'min_carat': Decimal('1.0'), 'color': 'D'}
            search_results = self.inventory_manager.search_diamonds(search_criteria)
            print(f"✓ نتائج البحث: {len(search_results)} قطعة")
            
            # حساب قيمة المخزون
            inventory_value = self.inventory_manager.get_inventory_value()
            print(f"✓ قيمة المخزون الإجمالية: {inventory_value['total_selling_value']}")
            
            # تعديل المخزون
            adjusted = self.inventory_manager.adjust_inventory(
                diamond_ids[0], 15, "تعديل تجريبي"
            )
            if adjusted:
                print("✓ تم تعديل المخزون")
            
        except Exception as e:
            print(f"✗ خطأ في اختبار المخزون: {e}")
    
    def test_currency_management(self):
        """اختبار إدارة العملات"""
        print("\nاختبار إدارة العملات...")
        
        try:
            # الحصول على العملات
            currencies = self.currency_manager.get_all_currencies()
            print(f"✓ عدد العملات المتاحة: {len(currencies)}")
            
            # تحويل العملات
            usd_currency = self.currency_manager.get_currency_by_code('USD')
            sar_currency = self.currency_manager.get_currency_by_code('SAR')
            
            if usd_currency and sar_currency:
                converted_amount = self.currency_manager.convert_amount(
                    Decimal('1000'),
                    usd_currency['CurrencyID'],
                    sar_currency['CurrencyID']
                )
                if converted_amount:
                    print(f"✓ تحويل 1000 دولار = {converted_amount} ريال")
            
        except Exception as e:
            print(f"✗ خطأ في اختبار العملات: {e}")
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("بدء اختبار النظام...")
        print("=" * 50)
        
        # إعداد قاعدة البيانات
        self.setup_database()
        
        # إضافة بيانات تجريبية
        sample_data = self.add_sample_data()
        
        # اختبار العمليات
        self.test_currency_management()
        self.test_inventory_management(sample_data['diamond_ids'])
        
        sale_id = self.test_sales_process(
            sample_data['customer_ids'], 
            sample_data['diamond_ids']
        )
        
        purchase_id = self.test_purchase_process(
            sample_data['supplier_ids'], 
            sample_data['diamond_ids']
        )
        
        print("\n" + "=" * 50)
        print("انتهى اختبار النظام!")
        
        if sale_id and purchase_id:
            print("✓ جميع الاختبارات نجحت!")
        else:
            print("⚠ بعض الاختبارات فشلت!")
        
        # إغلاق الاتصال
        self.db_schema.close()


if __name__ == "__main__":
    tester = SystemTester()
    tester.run_all_tests()

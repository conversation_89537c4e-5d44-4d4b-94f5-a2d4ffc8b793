"""
مدير العملات وأسعار الصرف
"""

import sqlite3
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from decimal import Decimal

from models.base import Currency, ExchangeRate
from database.repositories import CurrencyRepository, ExchangeRateRepository


class CurrencyManager:
    """مدير العملات وأسعار الصرف"""
    
    def __init__(self, connection: sqlite3.Connection):
        """
        تهيئة مدير العملات
        
        Args:
            connection: اتصال قاعدة البيانات
        """
        self.connection = connection
        self.currency_repo = CurrencyRepository(connection)
        self.exchange_rate_repo = ExchangeRateRepository(connection)
    
    def add_currency(self, currency_code: str, currency_name: str, 
                    symbol: str = "", is_base_currency: bool = False) -> int:
        """
        إضافة عملة جديدة
        
        Args:
            currency_code: رمز العملة (مثل USD, SAR)
            currency_name: اسم العملة
            symbol: رمز العملة ($, ر.س)
            is_base_currency: هل هي العملة الأساسية
            
        Returns:
            معرف العملة
        """
        # التحقق من عدم وجود العملة مسبقاً
        existing = self.currency_repo.get_by_code(currency_code)
        if existing:
            raise ValueError(f"العملة {currency_code} موجودة مسبقاً")
        
        # إذا كانت عملة أساسية، إلغاء تفعيل العملات الأساسية الأخرى
        if is_base_currency:
            self._unset_base_currencies()
        
        currency_data = {
            'currency_code': currency_code.upper(),
            'currency_name': currency_name,
            'symbol': symbol,
            'is_base_currency': is_base_currency,
            'created_at': datetime.now()
        }
        
        return self.currency_repo.insert(currency_data)
    
    def update_currency(self, currency_id: int, **kwargs) -> bool:
        """
        تحديث بيانات العملة
        
        Args:
            currency_id: معرف العملة
            **kwargs: البيانات المحدثة
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        # إذا كان التحديث يتضمن جعل العملة أساسية
        if kwargs.get('is_base_currency'):
            self._unset_base_currencies()
        
        return self.currency_repo.update(currency_id, kwargs)
    
    def get_currency_by_code(self, currency_code: str) -> Optional[Dict[str, Any]]:
        """
        الحصول على عملة بالرمز
        
        Args:
            currency_code: رمز العملة
            
        Returns:
            بيانات العملة
        """
        return self.currency_repo.get_by_code(currency_code.upper())
    
    def get_base_currency(self) -> Optional[Dict[str, Any]]:
        """
        الحصول على العملة الأساسية
        
        Returns:
            العملة الأساسية
        """
        return self.currency_repo.get_base_currency()
    
    def get_all_currencies(self) -> List[Dict[str, Any]]:
        """
        الحصول على جميع العملات
        
        Returns:
            قائمة العملات
        """
        return self.currency_repo.get_all()
    
    def add_exchange_rate(self, from_currency_id: int, to_currency_id: int, 
                         exchange_rate: Decimal, rate_date: date = None) -> int:
        """
        إضافة سعر صرف
        
        Args:
            from_currency_id: معرف العملة المصدر
            to_currency_id: معرف العملة الهدف
            exchange_rate: سعر الصرف
            rate_date: تاريخ السعر
            
        Returns:
            معرف سعر الصرف
        """
        if rate_date is None:
            rate_date = date.today()
        
        # التحقق من عدم وجود سعر لنفس التاريخ
        existing = self.exchange_rate_repo.get_rate_for_date(
            from_currency_id, to_currency_id, rate_date
        )
        
        if existing and existing['date'] == rate_date:
            # تحديث السعر الموجود
            return self.exchange_rate_repo.update(
                existing['rate_id'], 
                {'exchange_rate': exchange_rate}
            )
        
        rate_data = {
            'from_currency_id': from_currency_id,
            'to_currency_id': to_currency_id,
            'exchange_rate': exchange_rate,
            'date': rate_date,
            'created_at': datetime.now()
        }
        
        return self.exchange_rate_repo.insert(rate_data)
    
    def get_exchange_rate(self, from_currency_id: int, to_currency_id: int, 
                         target_date: date = None) -> Optional[Decimal]:
        """
        الحصول على سعر الصرف
        
        Args:
            from_currency_id: معرف العملة المصدر
            to_currency_id: معرف العملة الهدف
            target_date: التاريخ المطلوب
            
        Returns:
            سعر الصرف
        """
        # إذا كانت نفس العملة
        if from_currency_id == to_currency_id:
            return Decimal('1')
        
        if target_date is None:
            target_date = date.today()
        
        # البحث عن سعر مباشر
        rate_data = self.exchange_rate_repo.get_rate_for_date(
            from_currency_id, to_currency_id, target_date
        )
        
        if rate_data:
            return Decimal(str(rate_data['exchange_rate']))
        
        # البحث عن سعر عكسي
        reverse_rate_data = self.exchange_rate_repo.get_rate_for_date(
            to_currency_id, from_currency_id, target_date
        )
        
        if reverse_rate_data:
            reverse_rate = Decimal(str(reverse_rate_data['exchange_rate']))
            return Decimal('1') / reverse_rate if reverse_rate != 0 else None
        
        # البحث عن سعر عبر العملة الأساسية
        base_currency = self.get_base_currency()
        if base_currency:
            base_id = base_currency['currency_id']
            
            if from_currency_id != base_id and to_currency_id != base_id:
                # تحويل من العملة المصدر إلى الأساسية
                from_to_base = self.get_exchange_rate(from_currency_id, base_id, target_date)
                # تحويل من الأساسية إلى العملة الهدف
                base_to_target = self.get_exchange_rate(base_id, to_currency_id, target_date)
                
                if from_to_base and base_to_target:
                    return from_to_base * base_to_target
        
        return None
    
    def convert_amount(self, amount: Decimal, from_currency_id: int, 
                      to_currency_id: int, target_date: date = None) -> Optional[Decimal]:
        """
        تحويل مبلغ من عملة إلى أخرى
        
        Args:
            amount: المبلغ
            from_currency_id: معرف العملة المصدر
            to_currency_id: معرف العملة الهدف
            target_date: التاريخ المطلوب
            
        Returns:
            المبلغ المحول
        """
        rate = self.get_exchange_rate(from_currency_id, to_currency_id, target_date)
        
        if rate is not None:
            return amount * rate
        
        return None
    
    def get_currency_rates_history(self, currency_id: int, days: int = 30) -> List[Dict[str, Any]]:
        """
        الحصول على تاريخ أسعار العملة
        
        Args:
            currency_id: معرف العملة
            days: عدد الأيام
            
        Returns:
            تاريخ الأسعار
        """
        end_date = date.today()
        start_date = date.fromordinal(end_date.toordinal() - days)
        
        sql = """
        SELECT er.*, 
               fc.CurrencyCode as FromCurrencyCode,
               tc.CurrencyCode as ToCurrencyCode
        FROM ExchangeRates er
        JOIN Currencies fc ON er.FromCurrencyID = fc.CurrencyID
        JOIN Currencies tc ON er.ToCurrencyID = tc.CurrencyID
        WHERE (er.FromCurrencyID = ? OR er.ToCurrencyID = ?)
        AND er.Date BETWEEN ? AND ?
        ORDER BY er.Date DESC
        """
        
        return self.exchange_rate_repo.execute_query(
            sql, [currency_id, currency_id, start_date.isoformat(), end_date.isoformat()]
        )
    
    def update_daily_rates(self, rates: Dict[str, Dict[str, Decimal]]) -> bool:
        """
        تحديث الأسعار اليومية
        
        Args:
            rates: قاموس الأسعار {from_currency: {to_currency: rate}}
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        try:
            self.connection.execute("BEGIN TRANSACTION")
            
            today = date.today()
            
            for from_code, to_rates in rates.items():
                from_currency = self.get_currency_by_code(from_code)
                if not from_currency:
                    continue
                
                for to_code, rate in to_rates.items():
                    to_currency = self.get_currency_by_code(to_code)
                    if not to_currency:
                        continue
                    
                    self.add_exchange_rate(
                        from_currency['currency_id'],
                        to_currency['currency_id'],
                        rate,
                        today
                    )
            
            self.connection.commit()
            return True
            
        except Exception as e:
            self.connection.rollback()
            raise e
    
    def get_conversion_summary(self, amount: Decimal, from_currency_id: int) -> Dict[str, Any]:
        """
        الحصول على ملخص التحويل لجميع العملات
        
        Args:
            amount: المبلغ
            from_currency_id: معرف العملة المصدر
            
        Returns:
            ملخص التحويل
        """
        currencies = self.get_all_currencies()
        conversions = {}
        
        for currency in currencies:
            if currency['currency_id'] != from_currency_id:
                converted_amount = self.convert_amount(
                    amount, from_currency_id, currency['currency_id']
                )
                
                if converted_amount is not None:
                    conversions[currency['currency_code']] = {
                        'amount': converted_amount,
                        'currency_name': currency['currency_name'],
                        'symbol': currency['symbol']
                    }
        
        return conversions
    
    def _unset_base_currencies(self):
        """إلغاء تفعيل جميع العملات الأساسية"""
        sql = "UPDATE Currencies SET IsBaseCurrency = 0 WHERE IsBaseCurrency = 1"
        cursor = self.connection.cursor()
        cursor.execute(sql)
        self.connection.commit()

"""
مدير المخزون
"""

import sqlite3
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from decimal import Decimal

from models.base import Diamond, Inventory
from database.repositories import DiamondRepository, InventoryRepository


class InventoryManager:
    """مدير المخزون"""
    
    def __init__(self, connection: sqlite3.Connection):
        """
        تهيئة مدير المخزون
        
        Args:
            connection: اتصال قاعدة البيانات
        """
        self.connection = connection
        self.diamond_repo = DiamondRepository(connection)
        self.inventory_repo = InventoryRepository(connection)
    
    def add_diamond(self, carat: Decimal, color: str, clarity: str, cut: str, 
                   shape: str, certificate_number: str, cost_price: Decimal, 
                   selling_price: Decimal, currency_id: int, description: str = "") -> int:
        """
        إضافة ألماس جديد
        
        Args:
            carat: القيراط
            color: اللون
            clarity: النقاء
            cut: القطع
            shape: الشكل
            certificate_number: رقم الشهادة
            cost_price: سعر التكلفة
            selling_price: سعر البيع
            currency_id: معرف العملة
            description: الوصف
            
        Returns:
            معرف الألماس
        """
        diamond_data = {
            'Carat': carat,
            'Color': color,
            'Clarity': clarity,
            'Cut': cut,
            'Shape': shape,
            'CertificateNumber': certificate_number,
            'CostPrice': cost_price,
            'SellingPrice': selling_price,
            'CurrencyID': currency_id,
            'Description': description,
            'IsActive': True,
            'CreatedAt': datetime.now()
        }
        
        diamond_id = self.diamond_repo.insert(diamond_data)
        
        if diamond_id:
            # إنشاء سجل مخزون للألماس
            inventory_data = {
                'DiamondID': diamond_id,
                'Quantity': 0,
                'ReservedQuantity': 0,
                'LastUpdated': datetime.now()
            }
            self.inventory_repo.insert(inventory_data)
        
        return diamond_id
    
    def update_diamond(self, diamond_id: int, **kwargs) -> bool:
        """
        تحديث بيانات الألماس
        
        Args:
            diamond_id: معرف الألماس
            **kwargs: البيانات المحدثة
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        return self.diamond_repo.update(diamond_id, kwargs)
    
    def deactivate_diamond(self, diamond_id: int) -> bool:
        """
        إلغاء تفعيل الألماس
        
        Args:
            diamond_id: معرف الألماس
            
        Returns:
            True إذا تم الإلغاء بنجاح
        """
        return self.diamond_repo.update(diamond_id, {'is_active': False})
    
    def get_diamond_details(self, diamond_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على تفاصيل الألماس
        
        Args:
            diamond_id: معرف الألماس
            
        Returns:
            تفاصيل الألماس
        """
        diamond = self.diamond_repo.get_by_id(diamond_id)
        if diamond:
            # إضافة بيانات المخزون
            inventory = self.inventory_repo.get_by_diamond_id(diamond_id)
            if inventory:
                diamond.update(inventory)
        
        return diamond
    
    def search_diamonds(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        البحث عن الألماس
        
        Args:
            criteria: معايير البحث
            
        Returns:
            قائمة الألماس المطابق
        """
        diamonds = self.diamond_repo.search_diamonds(criteria)
        
        # إضافة بيانات المخزون لكل ألماس
        for diamond in diamonds:
            inventory = self.inventory_repo.get_by_diamond_id(diamond['DiamondID'])
            if inventory:
                diamond.update(inventory)
        
        return diamonds
    
    def get_available_inventory(self) -> List[Dict[str, Any]]:
        """
        الحصول على المخزون المتاح
        
        Returns:
            قائمة المخزون المتاح
        """
        return self.inventory_repo.get_available_inventory()
    
    def get_low_stock_items(self, threshold: int = 5) -> List[Dict[str, Any]]:
        """
        الحصول على العناصر منخفضة المخزون
        
        Args:
            threshold: الحد الأدنى للكمية
            
        Returns:
            قائمة العناصر منخفضة المخزون
        """
        sql = """
        SELECT i.*, d.Carat, d.Color, d.Clarity, d.Cut, d.Shape, d.SellingPrice
        FROM Inventory i
        JOIN Diamonds d ON i.DiamondID = d.DiamondID
        WHERE i.Quantity <= ? AND d.IsActive = 1
        ORDER BY i.Quantity ASC
        """
        return self.inventory_repo.execute_query(sql, [threshold])
    
    def get_inventory_value(self, currency_id: int = None) -> Dict[str, Decimal]:
        """
        حساب قيمة المخزون
        
        Args:
            currency_id: معرف العملة (اختياري)
            
        Returns:
            قيمة المخزون (تكلفة وبيع)
        """
        where_clause = "d.IsActive = 1"
        params = []
        
        if currency_id:
            where_clause += " AND d.CurrencyID = ?"
            params.append(currency_id)
        
        sql = f"""
        SELECT 
            SUM(i.Quantity * d.CostPrice) as TotalCostValue,
            SUM(i.Quantity * d.SellingPrice) as TotalSellingValue,
            COUNT(*) as TotalItems,
            SUM(i.Quantity) as TotalQuantity
        FROM Inventory i
        JOIN Diamonds d ON i.DiamondID = d.DiamondID
        WHERE {where_clause}
        """
        
        result = self.inventory_repo.execute_query(sql, params)
        
        if result and result[0]:
            data = result[0]
            return {
                'total_cost_value': Decimal(str(data['TotalCostValue'] or 0)),
                'total_selling_value': Decimal(str(data['TotalSellingValue'] or 0)),
                'total_items': data['TotalItems'] or 0,
                'total_quantity': data['TotalQuantity'] or 0
            }
        
        return {
            'total_cost_value': Decimal('0'),
            'total_selling_value': Decimal('0'),
            'total_items': 0,
            'total_quantity': 0
        }
    
    def get_inventory_movement_report(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """
        تقرير حركة المخزون
        
        Args:
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            تقرير حركة المخزون
        """
        # حركة المبيعات
        sales_sql = """
        SELECT 
            'SALE' as TransactionType,
            s.SaleDate as TransactionDate,
            si.DiamondID,
            d.Carat, d.Color, d.Clarity, d.Cut, d.Shape,
            -si.Quantity as QuantityChange,
            si.UnitPrice,
            'مبيعات رقم ' || s.SaleID as Reference
        FROM SaleItems si
        JOIN Sales s ON si.SaleID = s.SaleID
        JOIN Diamonds d ON si.DiamondID = d.DiamondID
        WHERE s.SaleDate BETWEEN ? AND ? AND s.PaymentStatus = 'COMPLETED'
        """
        
        # حركة المشتريات
        purchases_sql = """
        SELECT 
            'PURCHASE' as TransactionType,
            p.PurchaseDate as TransactionDate,
            pi.DiamondID,
            d.Carat, d.Color, d.Clarity, d.Cut, d.Shape,
            pi.Quantity as QuantityChange,
            pi.UnitPrice,
            'مشتريات رقم ' || p.PurchaseID as Reference
        FROM PurchaseItems pi
        JOIN Purchases p ON pi.PurchaseID = p.PurchaseID
        JOIN Diamonds d ON pi.DiamondID = d.DiamondID
        WHERE p.PurchaseDate BETWEEN ? AND ? AND p.PaymentStatus = 'COMPLETED'
        """
        
        # دمج النتائج
        combined_sql = f"""
        {sales_sql}
        UNION ALL
        {purchases_sql}
        ORDER BY TransactionDate DESC, TransactionType
        """
        
        params = [start_date.isoformat(), end_date.isoformat()] * 2
        return self.inventory_repo.execute_query(combined_sql, params)
    
    def adjust_inventory(self, diamond_id: int, new_quantity: int, reason: str = "") -> bool:
        """
        تعديل المخزون يدوياً
        
        Args:
            diamond_id: معرف الألماس
            new_quantity: الكمية الجديدة
            reason: سبب التعديل
            
        Returns:
            True إذا تم التعديل بنجاح
        """
        # الحصول على الكمية الحالية
        inventory = self.inventory_repo.get_by_diamond_id(diamond_id)
        if not inventory:
            return False
        
        current_quantity = inventory['Quantity']
        quantity_change = new_quantity - current_quantity
        
        # تحديث الكمية
        success = self.inventory_repo.update_quantity(diamond_id, quantity_change)
        
        if success:
            # يمكن إضافة سجل للتعديل هنا إذا لزم الأمر
            pass
        
        return success
    
    def get_diamond_history(self, diamond_id: int) -> Dict[str, Any]:
        """
        الحصول على تاريخ الألماس
        
        Args:
            diamond_id: معرف الألماس
            
        Returns:
            تاريخ الألماس
        """
        diamond = self.get_diamond_details(diamond_id)
        if not diamond:
            return {}
        
        # المبيعات
        sales_sql = """
        SELECT s.SaleDate, si.Quantity, si.UnitPrice, c.Name as CustomerName
        FROM SaleItems si
        JOIN Sales s ON si.SaleID = s.SaleID
        LEFT JOIN Customers c ON s.CustomerID = c.CustomerID
        WHERE si.DiamondID = ? AND s.PaymentStatus = 'COMPLETED'
        ORDER BY s.SaleDate DESC
        """
        
        # المشتريات
        purchases_sql = """
        SELECT p.PurchaseDate, pi.Quantity, pi.UnitPrice, sup.Name as SupplierName
        FROM PurchaseItems pi
        JOIN Purchases p ON pi.PurchaseID = p.PurchaseID
        LEFT JOIN Suppliers sup ON p.SupplierID = sup.SupplierID
        WHERE pi.DiamondID = ? AND p.PaymentStatus = 'COMPLETED'
        ORDER BY p.PurchaseDate DESC
        """
        
        sales_history = self.inventory_repo.execute_query(sales_sql, [diamond_id])
        purchase_history = self.inventory_repo.execute_query(purchases_sql, [diamond_id])
        
        return {
            'diamond': diamond,
            'sales_history': sales_history,
            'purchase_history': purchase_history
        }

# نظام إدارة مبيعات الألماس

نظام شامل لإدارة مبيعات الألماس يدعم العمليات التالية:

## الميزات الرئيسية

1. **البيع والشراء**: تسجيل عمليات البيع والشراء للألماس
2. **سندات القبض والصرف**: إدارة سندات القبض والصرف
3. **المخزون**: تتبع المخزون المتاح من الألماس
4. **النقدية**: إدارة التدفقات النقدية
5. **القيود اليومية**: تسجيل القيود المحاسبية اليومية
6. **الدليل المحاسبي**: إدارة الحسابات المحاسبية
7. **الدعم متعدد العملات**: دعم عمليات البيع والشراء بالدولار وحساب المقابل بالريال أو العملات الأخرى

## هيكل المشروع

```
diamonds/
├── models/          # نماذج البيانات
├── database/        # طبقة قاعدة البيانات
├── business/        # منطق الأعمال
├── ui/             # واجهة المستخدم
├── tests/          # الاختبارات
└── main.py         # نقطة البداية
```

## التشغيل

```bash
python main.py
```

## المتطلبات

- Python 3.8+
- SQLite3

"""
مدير المحاسبة والقيود اليومية
"""

import sqlite3
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from decimal import Decimal

from models.base import JournalEntry, JournalEntryDetail, ChartOfAccount
from database.repositories import (
    JournalEntryRepository, JournalEntryDetailRepository, 
    ChartOfAccountRepository, ReceiptRepository, PaymentRepository
)


class AccountingManager:
    """مدير المحاسبة والقيود اليومية"""
    
    def __init__(self, connection: sqlite3.Connection):
        """
        تهيئة مدير المحاسبة
        
        Args:
            connection: اتصال قاعدة البيانات
        """
        self.connection = connection
        self.journal_repo = JournalEntryRepository(connection)
        self.journal_detail_repo = JournalEntryDetailRepository(connection)
        self.account_repo = ChartOfAccountRepository(connection)
        self.receipt_repo = ReceiptRepository(connection)
        self.payment_repo = PaymentRepository(connection)
    
    def add_account(self, account_code: str, account_name: str, account_type: str,
                   parent_account_id: Optional[int] = None, description: str = "") -> int:
        """
        إضافة حساب جديد للدليل المحاسبي
        
        Args:
            account_code: رمز الحساب
            account_name: اسم الحساب
            account_type: نوع الحساب (ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE)
            parent_account_id: معرف الحساب الأب
            description: وصف الحساب
            
        Returns:
            معرف الحساب
        """
        # التحقق من عدم وجود الحساب مسبقاً
        existing = self.account_repo.get_by_code(account_code)
        if existing:
            raise ValueError(f"الحساب {account_code} موجود مسبقاً")
        
        account_data = {
            'account_code': account_code,
            'account_name': account_name,
            'account_type': account_type.upper(),
            'parent_account_id': parent_account_id,
            'is_active': True,
            'description': description,
            'created_at': datetime.now()
        }
        
        return self.account_repo.insert(account_data)
    
    def update_account(self, account_id: int, **kwargs) -> bool:
        """
        تحديث بيانات الحساب
        
        Args:
            account_id: معرف الحساب
            **kwargs: البيانات المحدثة
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        return self.account_repo.update(account_id, kwargs)
    
    def deactivate_account(self, account_id: int) -> bool:
        """
        إلغاء تفعيل الحساب
        
        Args:
            account_id: معرف الحساب
            
        Returns:
            True إذا تم الإلغاء بنجاح
        """
        return self.account_repo.update(account_id, {'is_active': False})
    
    def get_account_by_code(self, account_code: str) -> Optional[Dict[str, Any]]:
        """
        الحصول على حساب بالرمز
        
        Args:
            account_code: رمز الحساب
            
        Returns:
            بيانات الحساب
        """
        return self.account_repo.get_by_code(account_code)
    
    def get_accounts_by_type(self, account_type: str) -> List[Dict[str, Any]]:
        """
        الحصول على الحسابات بالنوع
        
        Args:
            account_type: نوع الحساب
            
        Returns:
            قائمة الحسابات
        """
        return self.account_repo.get_by_type(account_type.upper())
    
    def get_chart_of_accounts(self) -> List[Dict[str, Any]]:
        """
        الحصول على الدليل المحاسبي الكامل
        
        Returns:
            الدليل المحاسبي
        """
        return self.account_repo.get_all("IsActive = ?", [True])
    
    def create_journal_entry(self, entry_date: date, description: str, 
                           reference_number: str = "") -> int:
        """
        إنشاء قيد يومي جديد
        
        Args:
            entry_date: تاريخ القيد
            description: وصف القيد
            reference_number: رقم المرجع
            
        Returns:
            معرف القيد
        """
        entry_data = {
            'entry_date': entry_date,
            'description': description,
            'reference_number': reference_number,
            'total_debit': Decimal('0'),
            'total_credit': Decimal('0'),
            'is_posted': False,
            'created_at': datetime.now()
        }
        
        return self.journal_repo.insert(entry_data)
    
    def add_journal_entry_detail(self, entry_id: int, account_id: int, 
                                debit_amount: Decimal = Decimal('0'), 
                                credit_amount: Decimal = Decimal('0'), 
                                description: str = "") -> int:
        """
        إضافة تفصيل للقيد اليومي
        
        Args:
            entry_id: معرف القيد
            account_id: معرف الحساب
            debit_amount: مبلغ المدين
            credit_amount: مبلغ الدائن
            description: وصف التفصيل
            
        Returns:
            معرف التفصيل
        """
        # التحقق من أن أحد المبلغين فقط غير صفر
        if (debit_amount > 0 and credit_amount > 0) or (debit_amount == 0 and credit_amount == 0):
            raise ValueError("يجب أن يكون أحد المبلغين فقط أكبر من الصفر")
        
        detail_data = {
            'entry_id': entry_id,
            'account_id': account_id,
            'debit_amount': debit_amount,
            'credit_amount': credit_amount,
            'description': description
        }
        
        detail_id = self.journal_detail_repo.insert(detail_data)
        
        if detail_id:
            # تحديث إجماليات القيد
            self._update_journal_entry_totals(entry_id)
        
        return detail_id
    
    def remove_journal_entry_detail(self, detail_id: int) -> bool:
        """
        إزالة تفصيل من القيد اليومي
        
        Args:
            detail_id: معرف التفصيل
            
        Returns:
            True إذا تمت الإزالة بنجاح
        """
        # الحصول على معرف القيد
        detail = self.journal_detail_repo.get_by_id(detail_id, 'detail_id')
        if not detail:
            return False
        
        entry_id = detail['entry_id']
        
        # حذف التفصيل
        if self.journal_detail_repo.delete(detail_id, 'detail_id'):
            # تحديث إجماليات القيد
            self._update_journal_entry_totals(entry_id)
            return True
        
        return False
    
    def post_journal_entry(self, entry_id: int) -> bool:
        """
        ترحيل القيد اليومي
        
        Args:
            entry_id: معرف القيد
            
        Returns:
            True إذا تم الترحيل بنجاح
        """
        # الحصول على القيد مع التفاصيل
        entry = self.journal_repo.get_entry_with_details(entry_id)
        if not entry or not entry.get('details'):
            return False
        
        # التحقق من توازن القيد
        if entry['total_debit'] != entry['total_credit']:
            raise ValueError("القيد غير متوازن - إجمالي المدين لا يساوي إجمالي الدائن")
        
        # ترحيل القيد
        return self.journal_repo.update(entry_id, {'is_posted': True})
    
    def unpost_journal_entry(self, entry_id: int) -> bool:
        """
        إلغاء ترحيل القيد اليومي
        
        Args:
            entry_id: معرف القيد
            
        Returns:
            True إذا تم إلغاء الترحيل بنجاح
        """
        return self.journal_repo.update(entry_id, {'is_posted': False})
    
    def get_journal_entry_details(self, entry_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على تفاصيل القيد اليومي
        
        Args:
            entry_id: معرف القيد
            
        Returns:
            تفاصيل القيد
        """
        return self.journal_repo.get_entry_with_details(entry_id)
    
    def get_journal_entries_by_date_range(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """
        الحصول على القيود في فترة زمنية
        
        Args:
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            قائمة القيود
        """
        return self.journal_repo.get_entries_by_date_range(start_date, end_date)
    
    def get_unposted_entries(self) -> List[Dict[str, Any]]:
        """
        الحصول على القيود غير المرحلة
        
        Returns:
            قائمة القيود غير المرحلة
        """
        return self.journal_repo.get_unposted_entries()
    
    def get_account_balance(self, account_id: int, as_of_date: date = None) -> Dict[str, Decimal]:
        """
        حساب رصيد الحساب
        
        Args:
            account_id: معرف الحساب
            as_of_date: التاريخ المطلوب
            
        Returns:
            رصيد الحساب
        """
        if as_of_date is None:
            as_of_date = date.today()
        
        # الحصول على معاملات الحساب
        transactions = self.journal_detail_repo.get_account_transactions(
            account_id, end_date=as_of_date
        )
        
        total_debit = sum(Decimal(str(t['debit_amount'])) for t in transactions)
        total_credit = sum(Decimal(str(t['credit_amount'])) for t in transactions)
        
        # الحصول على نوع الحساب لتحديد الرصيد
        account = self.account_repo.get_by_id(account_id)
        account_type = account['account_type'] if account else 'ASSET'
        
        # حساب الرصيد حسب نوع الحساب
        if account_type in ['ASSET', 'EXPENSE']:
            balance = total_debit - total_credit
        else:  # LIABILITY, EQUITY, REVENUE
            balance = total_credit - total_debit
        
        return {
            'total_debit': total_debit,
            'total_credit': total_credit,
            'balance': balance,
            'account_type': account_type
        }
    
    def get_trial_balance(self, as_of_date: date = None) -> List[Dict[str, Any]]:
        """
        إعداد ميزان المراجعة
        
        Args:
            as_of_date: التاريخ المطلوب
            
        Returns:
            ميزان المراجعة
        """
        if as_of_date is None:
            as_of_date = date.today()
        
        accounts = self.get_chart_of_accounts()
        trial_balance = []
        
        total_debits = Decimal('0')
        total_credits = Decimal('0')
        
        for account in accounts:
            balance_info = self.get_account_balance(account['account_id'], as_of_date)
            
            if balance_info['total_debit'] != 0 or balance_info['total_credit'] != 0:
                debit_balance = balance_info['balance'] if balance_info['balance'] > 0 else Decimal('0')
                credit_balance = -balance_info['balance'] if balance_info['balance'] < 0 else Decimal('0')
                
                trial_balance.append({
                    'account_code': account['account_code'],
                    'account_name': account['account_name'],
                    'account_type': account['account_type'],
                    'debit_balance': debit_balance,
                    'credit_balance': credit_balance
                })
                
                total_debits += debit_balance
                total_credits += credit_balance
        
        # إضافة الإجماليات
        trial_balance.append({
            'account_code': '',
            'account_name': 'الإجماليات',
            'account_type': 'TOTAL',
            'debit_balance': total_debits,
            'credit_balance': total_credits
        })
        
        return trial_balance
    
    def create_sale_journal_entry(self, sale_id: int, sale_amount: Decimal, 
                                 customer_id: Optional[int] = None) -> int:
        """
        إنشاء قيد يومي للمبيعات
        
        Args:
            sale_id: معرف المبيعات
            sale_amount: مبلغ المبيعات
            customer_id: معرف العميل
            
        Returns:
            معرف القيد
        """
        # إنشاء القيد
        entry_id = self.create_journal_entry(
            date.today(),
            f"مبيعات رقم {sale_id}",
            f"SALE-{sale_id}"
        )
        
        # الحصول على الحسابات
        cash_account = self.get_account_by_code('1110')  # النقدية
        sales_account = self.get_account_by_code('4100')  # مبيعات الألماس
        
        if not cash_account or not sales_account:
            raise ValueError("الحسابات المطلوبة غير موجودة")
        
        # مدين: النقدية
        self.add_journal_entry_detail(
            entry_id, cash_account['account_id'], 
            debit_amount=sale_amount, description="نقدية من المبيعات"
        )
        
        # دائن: المبيعات
        self.add_journal_entry_detail(
            entry_id, sales_account['account_id'], 
            credit_amount=sale_amount, description="مبيعات الألماس"
        )
        
        return entry_id
    
    def create_purchase_journal_entry(self, purchase_id: int, purchase_amount: Decimal, 
                                     supplier_id: Optional[int] = None) -> int:
        """
        إنشاء قيد يومي للمشتريات
        
        Args:
            purchase_id: معرف المشتريات
            purchase_amount: مبلغ المشتريات
            supplier_id: معرف المورد
            
        Returns:
            معرف القيد
        """
        # إنشاء القيد
        entry_id = self.create_journal_entry(
            date.today(),
            f"مشتريات رقم {purchase_id}",
            f"PURCHASE-{purchase_id}"
        )
        
        # الحصول على الحسابات
        inventory_account = self.get_account_by_code('1120')  # المخزون
        cash_account = self.get_account_by_code('1110')  # النقدية
        
        if not inventory_account or not cash_account:
            raise ValueError("الحسابات المطلوبة غير موجودة")
        
        # مدين: المخزون
        self.add_journal_entry_detail(
            entry_id, inventory_account['account_id'], 
            debit_amount=purchase_amount, description="مشتريات ألماس"
        )
        
        # دائن: النقدية
        self.add_journal_entry_detail(
            entry_id, cash_account['account_id'], 
            credit_amount=purchase_amount, description="دفع نقدي للمشتريات"
        )
        
        return entry_id
    
    def _update_journal_entry_totals(self, entry_id: int):
        """
        تحديث إجماليات القيد اليومي
        
        Args:
            entry_id: معرف القيد
        """
        details = self.journal_detail_repo.get_by_entry_id(entry_id)
        
        total_debit = sum(Decimal(str(d['debit_amount'])) for d in details)
        total_credit = sum(Decimal(str(d['credit_amount'])) for d in details)
        
        self.journal_repo.update(entry_id, {
            'total_debit': total_debit,
            'total_credit': total_credit
        })

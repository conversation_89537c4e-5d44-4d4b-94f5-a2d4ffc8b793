"""
مستودع البيانات الأساسي
"""

import sqlite3
from typing import List, Optional, Dict, Any, Type, TypeVar
from datetime import datetime, date
from decimal import Decimal

from models.base import BaseModel

T = TypeVar('T', bound=BaseModel)


class BaseRepository:
    """مستودع البيانات الأساسي"""
    
    def __init__(self, connection: sqlite3.Connection, table_name: str):
        """
        تهيئة المستودع
        
        Args:
            connection: اتصال قاعدة البيانات
            table_name: اسم الجدول
        """
        self.connection = connection
        self.table_name = table_name
        self.connection.row_factory = sqlite3.Row
    
    def _convert_value(self, value: Any) -> Any:
        """تحويل القيم للتخزين في قاعدة البيانات"""
        if isinstance(value, Decimal):
            return float(value)
        elif isinstance(value, (datetime, date)):
            return value.isoformat()
        elif isinstance(value, bool):
            return int(value)
        return value
    
    def _convert_row_to_dict(self, row: sqlite3.Row) -> Dict[str, Any]:
        """تحويل صف قاعدة البيانات إلى قاموس"""
        if row is None:
            return {}
        
        result = {}
        for key in row.keys():
            value = row[key]
            # تحويل القيم من قاعدة البيانات
            if key.endswith('_date') and isinstance(value, str):
                try:
                    result[key] = datetime.fromisoformat(value).date()
                except:
                    result[key] = value
            elif key.endswith('_at') and isinstance(value, str):
                try:
                    result[key] = datetime.fromisoformat(value)
                except:
                    result[key] = value
            elif key.endswith(('_amount', '_price', '_rate')) and value is not None:
                result[key] = Decimal(str(value))
            elif key.endswith(('_active', '_posted', '_currency')) and value is not None:
                result[key] = bool(value)
            else:
                result[key] = value
        
        return result
    
    def insert(self, data: Dict[str, Any]) -> int:
        """
        إدراج سجل جديد
        
        Args:
            data: بيانات السجل
            
        Returns:
            معرف السجل المدرج
        """
        # إزالة المعرف الأساسي إذا كان موجوداً
        data = {k: v for k, v in data.items() if not k.endswith('_id') or v is not None}
        
        # تحويل القيم
        converted_data = {k: self._convert_value(v) for k, v in data.items()}
        
        columns = list(converted_data.keys())
        placeholders = ['?' for _ in columns]
        values = list(converted_data.values())
        
        sql = f"""
        INSERT INTO {self.table_name} ({', '.join(columns)})
        VALUES ({', '.join(placeholders)})
        """
        
        cursor = self.connection.cursor()
        cursor.execute(sql, values)
        self.connection.commit()
        
        return cursor.lastrowid
    
    def update(self, id_value: int, data: Dict[str, Any], id_column: str = None) -> bool:
        """
        تحديث سجل موجود
        
        Args:
            id_value: قيمة المعرف
            data: البيانات المحدثة
            id_column: اسم عمود المعرف
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        if id_column is None:
            # تحديد اسم العمود بناءً على اسم الجدول
            table_lower = self.table_name.lower()
            if table_lower == 'currencies':
                id_column = 'CurrencyID'
            elif table_lower == 'exchangerates':
                id_column = 'RateID'
            elif table_lower == 'customers':
                id_column = 'CustomerID'
            elif table_lower == 'suppliers':
                id_column = 'SupplierID'
            elif table_lower == 'diamonds':
                id_column = 'DiamondID'
            elif table_lower == 'inventory':
                id_column = 'InventoryID'
            elif table_lower == 'sales':
                id_column = 'SaleID'
            elif table_lower == 'saleitems':
                id_column = 'SaleItemID'
            elif table_lower == 'purchases':
                id_column = 'PurchaseID'
            elif table_lower == 'purchaseitems':
                id_column = 'PurchaseItemID'
            elif table_lower == 'receipts':
                id_column = 'ReceiptID'
            elif table_lower == 'payments':
                id_column = 'PaymentID'
            elif table_lower == 'cash':
                id_column = 'CashID'
            elif table_lower == 'chartofaccounts':
                id_column = 'AccountID'
            elif table_lower == 'journalentries':
                id_column = 'EntryID'
            elif table_lower == 'journalentrydetails':
                id_column = 'DetailID'
            else:
                id_column = f"{self.table_name}ID"
        
        # إزالة المعرف من البيانات
        data = {k: v for k, v in data.items() if k != id_column}
        
        if not data:
            return False
        
        # تحويل القيم
        converted_data = {k: self._convert_value(v) for k, v in data.items()}
        
        set_clause = ', '.join([f"{k} = ?" for k in converted_data.keys()])
        values = list(converted_data.values()) + [id_value]
        
        sql = f"UPDATE {self.table_name} SET {set_clause} WHERE {id_column} = ?"
        
        cursor = self.connection.cursor()
        cursor.execute(sql, values)
        self.connection.commit()
        
        return cursor.rowcount > 0
    
    def delete(self, id_value: int, id_column: str = None) -> bool:
        """
        حذف سجل
        
        Args:
            id_value: قيمة المعرف
            id_column: اسم عمود المعرف
            
        Returns:
            True إذا تم الحذف بنجاح
        """
        if id_column is None:
            # تحديد اسم العمود بناءً على اسم الجدول
            table_lower = self.table_name.lower()
            if table_lower == 'currencies':
                id_column = 'CurrencyID'
            elif table_lower == 'exchangerates':
                id_column = 'RateID'
            elif table_lower == 'customers':
                id_column = 'CustomerID'
            elif table_lower == 'suppliers':
                id_column = 'SupplierID'
            elif table_lower == 'diamonds':
                id_column = 'DiamondID'
            elif table_lower == 'inventory':
                id_column = 'InventoryID'
            elif table_lower == 'sales':
                id_column = 'SaleID'
            elif table_lower == 'saleitems':
                id_column = 'SaleItemID'
            elif table_lower == 'purchases':
                id_column = 'PurchaseID'
            elif table_lower == 'purchaseitems':
                id_column = 'PurchaseItemID'
            elif table_lower == 'receipts':
                id_column = 'ReceiptID'
            elif table_lower == 'payments':
                id_column = 'PaymentID'
            elif table_lower == 'cash':
                id_column = 'CashID'
            elif table_lower == 'chartofaccounts':
                id_column = 'AccountID'
            elif table_lower == 'journalentries':
                id_column = 'EntryID'
            elif table_lower == 'journalentrydetails':
                id_column = 'DetailID'
            else:
                id_column = f"{self.table_name}ID"
        
        sql = f"DELETE FROM {self.table_name} WHERE {id_column} = ?"
        
        cursor = self.connection.cursor()
        cursor.execute(sql, [id_value])
        self.connection.commit()
        
        return cursor.rowcount > 0
    
    def get_by_id(self, id_value: int, id_column: str = None) -> Optional[Dict[str, Any]]:
        """
        الحصول على سجل بالمعرف
        
        Args:
            id_value: قيمة المعرف
            id_column: اسم عمود المعرف
            
        Returns:
            السجل أو None
        """
        if id_column is None:
            # تحديد اسم العمود بناءً على اسم الجدول
            table_lower = self.table_name.lower()
            if table_lower == 'currencies':
                id_column = 'CurrencyID'
            elif table_lower == 'exchangerates':
                id_column = 'RateID'
            elif table_lower == 'customers':
                id_column = 'CustomerID'
            elif table_lower == 'suppliers':
                id_column = 'SupplierID'
            elif table_lower == 'diamonds':
                id_column = 'DiamondID'
            elif table_lower == 'inventory':
                id_column = 'InventoryID'
            elif table_lower == 'sales':
                id_column = 'SaleID'
            elif table_lower == 'saleitems':
                id_column = 'SaleItemID'
            elif table_lower == 'purchases':
                id_column = 'PurchaseID'
            elif table_lower == 'purchaseitems':
                id_column = 'PurchaseItemID'
            elif table_lower == 'receipts':
                id_column = 'ReceiptID'
            elif table_lower == 'payments':
                id_column = 'PaymentID'
            elif table_lower == 'cash':
                id_column = 'CashID'
            elif table_lower == 'chartofaccounts':
                id_column = 'AccountID'
            elif table_lower == 'journalentries':
                id_column = 'EntryID'
            elif table_lower == 'journalentrydetails':
                id_column = 'DetailID'
            else:
                id_column = f"{self.table_name}ID"
        
        sql = f"SELECT * FROM {self.table_name} WHERE {id_column} = ?"
        
        cursor = self.connection.cursor()
        cursor.execute(sql, [id_value])
        row = cursor.fetchone()
        
        return self._convert_row_to_dict(row) if row else None
    
    def get_all(self, where_clause: str = "", params: List[Any] = None) -> List[Dict[str, Any]]:
        """
        الحصول على جميع السجلات
        
        Args:
            where_clause: شرط WHERE
            params: معاملات الاستعلام
            
        Returns:
            قائمة السجلات
        """
        if params is None:
            params = []
        
        sql = f"SELECT * FROM {self.table_name}"
        if where_clause:
            sql += f" WHERE {where_clause}"
        
        cursor = self.connection.cursor()
        cursor.execute(sql, params)
        rows = cursor.fetchall()
        
        return [self._convert_row_to_dict(row) for row in rows]
    
    def count(self, where_clause: str = "", params: List[Any] = None) -> int:
        """
        عد السجلات
        
        Args:
            where_clause: شرط WHERE
            params: معاملات الاستعلام
            
        Returns:
            عدد السجلات
        """
        if params is None:
            params = []
        
        sql = f"SELECT COUNT(*) FROM {self.table_name}"
        if where_clause:
            sql += f" WHERE {where_clause}"
        
        cursor = self.connection.cursor()
        cursor.execute(sql, params)
        
        return cursor.fetchone()[0]
    
    def execute_query(self, sql: str, params: List[Any] = None) -> List[Dict[str, Any]]:
        """
        تنفيذ استعلام مخصص
        
        Args:
            sql: الاستعلام
            params: المعاملات
            
        Returns:
            نتائج الاستعلام
        """
        if params is None:
            params = []
        
        cursor = self.connection.cursor()
        cursor.execute(sql, params)
        rows = cursor.fetchall()
        
        return [self._convert_row_to_dict(row) for row in rows]

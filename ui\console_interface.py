"""
واجهة سطر الأوامر لنظام إدارة مبيعات الألماس
"""

import os
import sys
from typing import Optional, List, Dict, Any
from datetime import date, datetime
from decimal import Decimal

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.schema import DatabaseSchema
from database.repositories import *
from business.sales_manager import SalesManager
from business.purchase_manager import PurchaseManager
from business.inventory_manager import InventoryManager
from business.currency_manager import CurrencyManager
from business.accounting_manager import AccountingManager


class ConsoleInterface:
    """واجهة سطر الأوامر"""
    
    def __init__(self):
        """تهيئة الواجهة"""
        self.db_schema = DatabaseSchema()
        self.connection = self.db_schema.connect()
        
        # تهيئة المدراء
        self.sales_manager = SalesManager(self.connection)
        self.purchase_manager = PurchaseManager(self.connection)
        self.inventory_manager = InventoryManager(self.connection)
        self.currency_manager = CurrencyManager(self.connection)
        self.accounting_manager = AccountingManager(self.connection)
        
        # تهيئة المستودعات
        self.customer_repo = CustomerRepository(self.connection)
        self.supplier_repo = SupplierRepository(self.connection)
        self.diamond_repo = DiamondRepository(self.connection)
        self.receipt_repo = ReceiptRepository(self.connection)
        self.payment_repo = PaymentRepository(self.connection)
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        print("جاري تهيئة قاعدة البيانات...")
        self.db_schema.create_tables()
        self.db_schema.insert_default_data()
        print("تم تهيئة قاعدة البيانات بنجاح!")
    
    def show_main_menu(self):
        """عرض القائمة الرئيسية"""
        while True:
            print("\n" + "="*50)
            print("نظام إدارة مبيعات الألماس")
            print("="*50)
            print("1. إدارة العملاء")
            print("2. إدارة الموردين")
            print("3. إدارة الألماس والمخزون")
            print("4. المبيعات")
            print("5. المشتريات")
            print("6. سندات القبض والصرف")
            print("7. إدارة العملات")
            print("8. المحاسبة والقيود")
            print("9. التقارير")
            print("0. خروج")
            print("-"*50)
            
            choice = input("اختر من القائمة: ").strip()
            
            if choice == "1":
                self.customer_menu()
            elif choice == "2":
                self.supplier_menu()
            elif choice == "3":
                self.inventory_menu()
            elif choice == "4":
                self.sales_menu()
            elif choice == "5":
                self.purchase_menu()
            elif choice == "6":
                self.receipts_payments_menu()
            elif choice == "7":
                self.currency_menu()
            elif choice == "8":
                self.accounting_menu()
            elif choice == "9":
                self.reports_menu()
            elif choice == "0":
                print("شكراً لاستخدام النظام!")
                break
            else:
                print("خيار غير صحيح، حاول مرة أخرى.")
    
    def customer_menu(self):
        """قائمة إدارة العملاء"""
        while True:
            print("\n--- إدارة العملاء ---")
            print("1. إضافة عميل جديد")
            print("2. عرض جميع العملاء")
            print("3. البحث عن عميل")
            print("4. تحديث بيانات عميل")
            print("0. العودة للقائمة الرئيسية")
            
            choice = input("اختر من القائمة: ").strip()
            
            if choice == "1":
                self.add_customer()
            elif choice == "2":
                self.list_customers()
            elif choice == "3":
                self.search_customers()
            elif choice == "4":
                self.update_customer()
            elif choice == "0":
                break
            else:
                print("خيار غير صحيح.")
    
    def add_customer(self):
        """إضافة عميل جديد"""
        print("\n--- إضافة عميل جديد ---")
        name = input("اسم العميل: ").strip()
        if not name:
            print("اسم العميل مطلوب!")
            return
        
        contact_info = input("معلومات الاتصال: ").strip()
        address = input("العنوان: ").strip()
        email = input("البريد الإلكتروني: ").strip()
        phone = input("رقم الهاتف: ").strip()
        
        customer_data = {
            'name': name,
            'contact_info': contact_info,
            'address': address,
            'email': email,
            'phone': phone,
            'is_active': True,
            'created_at': datetime.now()
        }
        
        try:
            customer_id = self.customer_repo.insert(customer_data)
            print(f"تم إضافة العميل بنجاح! معرف العميل: {customer_id}")
        except Exception as e:
            print(f"خطأ في إضافة العميل: {e}")
    
    def list_customers(self):
        """عرض جميع العملاء"""
        print("\n--- قائمة العملاء ---")
        customers = self.customer_repo.get_active_customers()
        
        if not customers:
            print("لا توجد عملاء مسجلين.")
            return
        
        print(f"{'المعرف':<5} {'الاسم':<20} {'الهاتف':<15} {'البريد الإلكتروني':<25}")
        print("-" * 65)
        
        for customer in customers:
            print(f"{customer['customer_id']:<5} {customer['name']:<20} "
                  f"{customer['phone']:<15} {customer['email']:<25}")
    
    def search_customers(self):
        """البحث عن عملاء"""
        print("\n--- البحث عن عملاء ---")
        name = input("أدخل اسم العميل للبحث: ").strip()
        
        if not name:
            print("يجب إدخال اسم للبحث!")
            return
        
        customers = self.customer_repo.search_by_name(name)
        
        if not customers:
            print("لم يتم العثور على عملاء.")
            return
        
        print(f"تم العثور على {len(customers)} عميل:")
        print(f"{'المعرف':<5} {'الاسم':<20} {'الهاتف':<15}")
        print("-" * 40)
        
        for customer in customers:
            print(f"{customer['customer_id']:<5} {customer['name']:<20} {customer['phone']:<15}")
    
    def update_customer(self):
        """تحديث بيانات عميل"""
        print("\n--- تحديث بيانات عميل ---")
        try:
            customer_id = int(input("معرف العميل: ").strip())
        except ValueError:
            print("معرف العميل يجب أن يكون رقماً!")
            return
        
        customer = self.customer_repo.get_by_id(customer_id)
        if not customer:
            print("العميل غير موجود!")
            return
        
        print(f"البيانات الحالية للعميل: {customer['name']}")
        print("اتركه فارغاً للاحتفاظ بالقيمة الحالية")
        
        name = input(f"الاسم الجديد ({customer['name']}): ").strip()
        phone = input(f"الهاتف الجديد ({customer['phone']}): ").strip()
        email = input(f"البريد الإلكتروني الجديد ({customer['email']}): ").strip()
        
        update_data = {}
        if name:
            update_data['name'] = name
        if phone:
            update_data['phone'] = phone
        if email:
            update_data['email'] = email
        
        if update_data:
            try:
                self.customer_repo.update(customer_id, update_data)
                print("تم تحديث بيانات العميل بنجاح!")
            except Exception as e:
                print(f"خطأ في تحديث البيانات: {e}")
        else:
            print("لم يتم تحديث أي بيانات.")
    
    def supplier_menu(self):
        """قائمة إدارة الموردين"""
        while True:
            print("\n--- إدارة الموردين ---")
            print("1. إضافة مورد جديد")
            print("2. عرض جميع الموردين")
            print("3. البحث عن مورد")
            print("0. العودة للقائمة الرئيسية")
            
            choice = input("اختر من القائمة: ").strip()
            
            if choice == "1":
                self.add_supplier()
            elif choice == "2":
                self.list_suppliers()
            elif choice == "3":
                self.search_suppliers()
            elif choice == "0":
                break
            else:
                print("خيار غير صحيح.")
    
    def add_supplier(self):
        """إضافة مورد جديد"""
        print("\n--- إضافة مورد جديد ---")
        name = input("اسم المورد: ").strip()
        if not name:
            print("اسم المورد مطلوب!")
            return
        
        contact_info = input("معلومات الاتصال: ").strip()
        address = input("العنوان: ").strip()
        email = input("البريد الإلكتروني: ").strip()
        phone = input("رقم الهاتف: ").strip()
        
        supplier_data = {
            'name': name,
            'contact_info': contact_info,
            'address': address,
            'email': email,
            'phone': phone,
            'is_active': True,
            'created_at': datetime.now()
        }
        
        try:
            supplier_id = self.supplier_repo.insert(supplier_data)
            print(f"تم إضافة المورد بنجاح! معرف المورد: {supplier_id}")
        except Exception as e:
            print(f"خطأ في إضافة المورد: {e}")
    
    def list_suppliers(self):
        """عرض جميع الموردين"""
        print("\n--- قائمة الموردين ---")
        suppliers = self.supplier_repo.get_active_suppliers()
        
        if not suppliers:
            print("لا توجد موردين مسجلين.")
            return
        
        print(f"{'المعرف':<5} {'الاسم':<20} {'الهاتف':<15} {'البريد الإلكتروني':<25}")
        print("-" * 65)
        
        for supplier in suppliers:
            print(f"{supplier['supplier_id']:<5} {supplier['name']:<20} "
                  f"{supplier['phone']:<15} {supplier['email']:<25}")
    
    def search_suppliers(self):
        """البحث عن موردين"""
        print("\n--- البحث عن موردين ---")
        name = input("أدخل اسم المورد للبحث: ").strip()
        
        if not name:
            print("يجب إدخال اسم للبحث!")
            return
        
        suppliers = self.supplier_repo.search_by_name(name)
        
        if not suppliers:
            print("لم يتم العثور على موردين.")
            return
        
        print(f"تم العثور على {len(suppliers)} مورد:")
        print(f"{'المعرف':<5} {'الاسم':<20} {'الهاتف':<15}")
        print("-" * 40)
        
        for supplier in suppliers:
            print(f"{supplier['supplier_id']:<5} {supplier['name']:<20} {supplier['phone']:<15}")
    
    def inventory_menu(self):
        """قائمة إدارة المخزون"""
        while True:
            print("\n--- إدارة الألماس والمخزون ---")
            print("1. إضافة ألماس جديد")
            print("2. عرض المخزون المتاح")
            print("3. البحث عن ألماس")
            print("4. تعديل كمية المخزون")
            print("0. العودة للقائمة الرئيسية")

            choice = input("اختر من القائمة: ").strip()

            if choice == "1":
                self.add_diamond()
            elif choice == "2":
                self.list_inventory()
            elif choice == "3":
                self.search_diamonds()
            elif choice == "4":
                self.adjust_inventory()
            elif choice == "0":
                break
            else:
                print("خيار غير صحيح.")

    def add_diamond(self):
        """إضافة ألماس جديد"""
        print("\n--- إضافة ألماس جديد ---")
        try:
            carat = Decimal(input("القيراط: ").strip())
            color = input("اللون: ").strip()
            clarity = input("النقاء: ").strip()
            cut = input("القطع: ").strip()
            shape = input("الشكل: ").strip()
            certificate_number = input("رقم الشهادة: ").strip()
            cost_price = Decimal(input("سعر التكلفة: ").strip())
            selling_price = Decimal(input("سعر البيع: ").strip())
            description = input("الوصف (اختياري): ").strip()

            # الحصول على العملة الافتراضية
            base_currency = self.currency_manager.get_base_currency()
            currency_id = base_currency['currency_id'] if base_currency else 1

            diamond_id = self.inventory_manager.add_diamond(
                carat, color, clarity, cut, shape, certificate_number,
                cost_price, selling_price, currency_id, description
            )

            print(f"تم إضافة الألماس بنجاح! معرف الألماس: {diamond_id}")

        except ValueError as e:
            print(f"خطأ في البيانات المدخلة: {e}")
        except Exception as e:
            print(f"خطأ في إضافة الألماس: {e}")

    def list_inventory(self):
        """عرض المخزون المتاح"""
        print("\n--- المخزون المتاح ---")
        inventory = self.inventory_manager.get_available_inventory()

        if not inventory:
            print("لا توجد عناصر في المخزون.")
            return

        print(f"{'المعرف':<5} {'القيراط':<8} {'اللون':<8} {'النقاء':<8} {'الكمية':<6} {'السعر':<10}")
        print("-" * 55)

        for item in inventory:
            print(f"{item['diamond_id']:<5} {item['carat']:<8} {item['color']:<8} "
                  f"{item['clarity']:<8} {item['quantity']:<6} {item['selling_price']:<10}")

    def search_diamonds(self):
        """البحث عن ألماس"""
        print("\n--- البحث عن ألماس ---")
        print("اتركه فارغاً لتجاهل المعيار")

        criteria = {}

        min_carat = input("الحد الأدنى للقيراط: ").strip()
        if min_carat:
            try:
                criteria['min_carat'] = Decimal(min_carat)
            except ValueError:
                print("قيمة القيراط غير صحيحة")
                return

        max_carat = input("الحد الأقصى للقيراط: ").strip()
        if max_carat:
            try:
                criteria['max_carat'] = Decimal(max_carat)
            except ValueError:
                print("قيمة القيراط غير صحيحة")
                return

        color = input("اللون: ").strip()
        if color:
            criteria['color'] = color

        clarity = input("النقاء: ").strip()
        if clarity:
            criteria['clarity'] = clarity

        diamonds = self.inventory_manager.search_diamonds(criteria)

        if not diamonds:
            print("لم يتم العثور على ألماس مطابق.")
            return

        print(f"تم العثور على {len(diamonds)} قطعة:")
        print(f"{'المعرف':<5} {'القيراط':<8} {'اللون':<8} {'النقاء':<8} {'الكمية':<6}")
        print("-" * 45)

        for diamond in diamonds:
            quantity = diamond.get('quantity', 0)
            print(f"{diamond['diamond_id']:<5} {diamond['carat']:<8} {diamond['color']:<8} "
                  f"{diamond['clarity']:<8} {quantity:<6}")

    def adjust_inventory(self):
        """تعديل كمية المخزون"""
        print("\n--- تعديل كمية المخزون ---")
        try:
            diamond_id = int(input("معرف الألماس: ").strip())
            new_quantity = int(input("الكمية الجديدة: ").strip())
            reason = input("سبب التعديل: ").strip()

            success = self.inventory_manager.adjust_inventory(diamond_id, new_quantity, reason)

            if success:
                print("تم تعديل المخزون بنجاح!")
            else:
                print("فشل في تعديل المخزون. تأكد من صحة معرف الألماس.")

        except ValueError:
            print("يجب إدخال أرقام صحيحة!")
        except Exception as e:
            print(f"خطأ في تعديل المخزون: {e}")

    def sales_menu(self):
        """قائمة المبيعات"""
        print("\n--- المبيعات ---")
        print("هذه الوظيفة قيد التطوير...")

    def purchase_menu(self):
        """قائمة المشتريات"""
        print("\n--- المشتريات ---")
        print("هذه الوظيفة قيد التطوير...")

    def receipts_payments_menu(self):
        """قائمة سندات القبض والصرف"""
        print("\n--- سندات القبض والصرف ---")
        print("هذه الوظيفة قيد التطوير...")

    def currency_menu(self):
        """قائمة إدارة العملات"""
        print("\n--- إدارة العملات ---")
        print("هذه الوظيفة قيد التطوير...")

    def accounting_menu(self):
        """قائمة المحاسبة"""
        print("\n--- المحاسبة والقيود ---")
        print("هذه الوظيفة قيد التطوير...")

    def reports_menu(self):
        """قائمة التقارير"""
        print("\n--- التقارير ---")
        print("هذه الوظيفة قيد التطوير...")

    def run(self):
        """تشغيل الواجهة"""
        print("مرحباً بك في نظام إدارة مبيعات الألماس")

        # التحقق من وجود قاعدة البيانات
        try:
            # محاولة الاستعلام عن الجداول
            cursor = self.connection.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            if not tables:
                print("قاعدة البيانات فارغة، سيتم تهيئتها...")
                self.initialize_database()
        except Exception as e:
            print(f"خطأ في قاعدة البيانات: {e}")
            print("سيتم إنشاء قاعدة بيانات جديدة...")
            self.initialize_database()

        # عرض القائمة الرئيسية
        self.show_main_menu()

        # إغلاق الاتصال
        self.db_schema.close()


if __name__ == "__main__":
    interface = ConsoleInterface()
    interface.run()

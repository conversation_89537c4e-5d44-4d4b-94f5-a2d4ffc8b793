#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل تطبيق سطح المكتب لنظام إدارة مبيعات الألماس
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.desktop_app import DiamondManagementApp
except ImportError as e:
    print(f"خطأ في استيراد التطبيق: {e}")
    sys.exit(1)


def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من توفر tkinter
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة المؤقتة
        
        # إنشاء وتشغيل التطبيق
        app = DiamondManagementApp()
        app.run()
        
    except tk.TclError as e:
        print(f"خطأ في واجهة المستخدم الرسومية: {e}")
        print("تأكد من تثبيت tkinter بشكل صحيح")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nتم إيقاف التطبيق بواسطة المستخدم.")
        sys.exit(0)
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        try:
            messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{e}")
        except:
            pass
        sys.exit(1)


if __name__ == "__main__":
    main()

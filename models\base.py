"""
النماذج الأساسية للنظام
"""

from dataclasses import dataclass
from datetime import datetime, date
from typing import Optional, List
from decimal import Decimal


@dataclass
class BaseModel:
    """النموذج الأساسي لجميع الكيانات"""
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> dict:
        """تحويل الكائن إلى قاموس"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, (datetime, date)):
                result[key] = value.isoformat()
            elif isinstance(value, Decimal):
                result[key] = float(value)
            else:
                result[key] = value
        return result


@dataclass
class Currency(BaseModel):
    """نموذج العملة"""
    currency_id: Optional[int] = None
    currency_code: str = ""
    currency_name: str = ""
    symbol: str = ""
    is_base_currency: bool = False


@dataclass
class ExchangeRate(BaseModel):
    """نموذج سعر الصرف"""
    rate_id: Optional[int] = None
    from_currency_id: int = 0
    to_currency_id: int = 0
    exchange_rate: Decimal = Decimal('0')
    date: date = date.today()


@dataclass
class Customer(BaseModel):
    """نموذج العميل"""
    customer_id: Optional[int] = None
    name: str = ""
    contact_info: str = ""
    address: str = ""
    email: str = ""
    phone: str = ""
    is_active: bool = True


@dataclass
class Supplier(BaseModel):
    """نموذج المورد"""
    supplier_id: Optional[int] = None
    name: str = ""
    contact_info: str = ""
    address: str = ""
    email: str = ""
    phone: str = ""
    is_active: bool = True


@dataclass
class Diamond(BaseModel):
    """نموذج الألماس"""
    diamond_id: Optional[int] = None
    carat: Decimal = Decimal('0')
    color: str = ""
    clarity: str = ""
    cut: str = ""
    shape: str = ""
    certificate_number: str = ""
    cost_price: Decimal = Decimal('0')
    selling_price: Decimal = Decimal('0')
    currency_id: int = 1
    description: str = ""
    is_active: bool = True


@dataclass
class Inventory(BaseModel):
    """نموذج المخزون"""
    inventory_id: Optional[int] = None
    diamond_id: int = 0
    quantity: int = 0
    reserved_quantity: int = 0
    available_quantity: int = 0
    last_updated: Optional[datetime] = None


@dataclass
class Sale(BaseModel):
    """نموذج المبيعات"""
    sale_id: Optional[int] = None
    customer_id: Optional[int] = None
    sale_date: date = date.today()
    total_amount: Decimal = Decimal('0')
    currency_id: int = 1
    payment_status: str = "PENDING"
    notes: str = ""
    items: List['SaleItem'] = None
    
    def __post_init__(self):
        if self.items is None:
            self.items = []


@dataclass
class SaleItem(BaseModel):
    """نموذج عنصر المبيعات"""
    sale_item_id: Optional[int] = None
    sale_id: int = 0
    diamond_id: int = 0
    quantity: int = 0
    unit_price: Decimal = Decimal('0')
    total_price: Decimal = Decimal('0')


@dataclass
class Purchase(BaseModel):
    """نموذج المشتريات"""
    purchase_id: Optional[int] = None
    supplier_id: Optional[int] = None
    purchase_date: date = date.today()
    total_amount: Decimal = Decimal('0')
    currency_id: int = 1
    payment_status: str = "PENDING"
    notes: str = ""
    items: List['PurchaseItem'] = None
    
    def __post_init__(self):
        if self.items is None:
            self.items = []


@dataclass
class PurchaseItem(BaseModel):
    """نموذج عنصر المشتريات"""
    purchase_item_id: Optional[int] = None
    purchase_id: int = 0
    diamond_id: int = 0
    quantity: int = 0
    unit_price: Decimal = Decimal('0')
    total_price: Decimal = Decimal('0')


@dataclass
class Receipt(BaseModel):
    """نموذج سند القبض"""
    receipt_id: Optional[int] = None
    customer_id: Optional[int] = None
    amount: Decimal = Decimal('0')
    receipt_date: date = date.today()
    currency_id: int = 1
    payment_method: str = ""
    reference_number: str = ""
    notes: str = ""


@dataclass
class Payment(BaseModel):
    """نموذج سند الصرف"""
    payment_id: Optional[int] = None
    supplier_id: Optional[int] = None
    amount: Decimal = Decimal('0')
    payment_date: date = date.today()
    currency_id: int = 1
    payment_method: str = ""
    reference_number: str = ""
    notes: str = ""


@dataclass
class Cash(BaseModel):
    """نموذج النقدية"""
    cash_id: Optional[int] = None
    amount: Decimal = Decimal('0')
    currency_id: int = 1
    transaction_date: date = date.today()
    transaction_type: str = ""
    description: str = ""
    reference_id: Optional[int] = None
    reference_type: str = ""


@dataclass
class ChartOfAccount(BaseModel):
    """نموذج الدليل المحاسبي"""
    account_id: Optional[int] = None
    account_code: str = ""
    account_name: str = ""
    account_type: str = ""
    parent_account_id: Optional[int] = None
    is_active: bool = True
    description: str = ""


@dataclass
class JournalEntry(BaseModel):
    """نموذج القيد اليومي"""
    entry_id: Optional[int] = None
    entry_date: date = date.today()
    description: str = ""
    reference_number: str = ""
    total_debit: Decimal = Decimal('0')
    total_credit: Decimal = Decimal('0')
    is_posted: bool = False
    details: List['JournalEntryDetail'] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = []


@dataclass
class JournalEntryDetail(BaseModel):
    """نموذج تفاصيل القيد اليومي"""
    detail_id: Optional[int] = None
    entry_id: int = 0
    account_id: int = 0
    debit_amount: Decimal = Decimal('0')
    credit_amount: Decimal = Decimal('0')
    description: str = ""

"""
مستودعات البيانات المتخصصة
"""

import sqlite3
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from decimal import Decimal

from .base_repository import BaseRepository
from models.base import *


class CurrencyRepository(BaseRepository):
    """مستودع العملات"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "Currencies")
    
    def get_by_code(self, currency_code: str) -> Optional[Dict[str, Any]]:
        """الحصول على عملة بالرمز"""
        results = self.get_all("CurrencyCode = ?", [currency_code])
        return results[0] if results else None
    
    def get_base_currency(self) -> Optional[Dict[str, Any]]:
        """الحصول على العملة الأساسية"""
        results = self.get_all("IsBaseCurrency = ?", [True])
        return results[0] if results else None


class ExchangeRateRepository(BaseRepository):
    """مستودع أسعار الصرف"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "ExchangeRates")
    
    def get_latest_rate(self, from_currency_id: int, to_currency_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على أحدث سعر صرف"""
        sql = """
        SELECT * FROM ExchangeRates 
        WHERE FromCurrencyID = ? AND ToCurrencyID = ?
        ORDER BY Date DESC, CreatedAt DESC
        LIMIT 1
        """
        results = self.execute_query(sql, [from_currency_id, to_currency_id])
        return results[0] if results else None
    
    def get_rate_for_date(self, from_currency_id: int, to_currency_id: int, target_date: date) -> Optional[Dict[str, Any]]:
        """الحصول على سعر الصرف لتاريخ محدد"""
        sql = """
        SELECT * FROM ExchangeRates 
        WHERE FromCurrencyID = ? AND ToCurrencyID = ? AND Date <= ?
        ORDER BY Date DESC, CreatedAt DESC
        LIMIT 1
        """
        results = self.execute_query(sql, [from_currency_id, to_currency_id, target_date.isoformat()])
        return results[0] if results else None


class CustomerRepository(BaseRepository):
    """مستودع العملاء"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "Customers")
    
    def get_active_customers(self) -> List[Dict[str, Any]]:
        """الحصول على العملاء النشطين"""
        return self.get_all("IsActive = ?", [True])
    
    def search_by_name(self, name: str) -> List[Dict[str, Any]]:
        """البحث عن عملاء بالاسم"""
        return self.get_all("Name LIKE ? AND IsActive = ?", [f"%{name}%", True])


class SupplierRepository(BaseRepository):
    """مستودع الموردين"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "Suppliers")
    
    def get_active_suppliers(self) -> List[Dict[str, Any]]:
        """الحصول على الموردين النشطين"""
        return self.get_all("IsActive = ?", [True])
    
    def search_by_name(self, name: str) -> List[Dict[str, Any]]:
        """البحث عن موردين بالاسم"""
        return self.get_all("Name LIKE ? AND IsActive = ?", [f"%{name}%", True])


class DiamondRepository(BaseRepository):
    """مستودع الألماس"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "Diamonds")
    
    def get_active_diamonds(self) -> List[Dict[str, Any]]:
        """الحصول على الألماس النشط"""
        return self.get_all("IsActive = ?", [True])
    
    def search_diamonds(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """البحث عن الألماس بمعايير متعددة"""
        where_parts = ["IsActive = ?"]
        params = [True]
        
        if criteria.get('min_carat'):
            where_parts.append("Carat >= ?")
            params.append(float(criteria['min_carat']))
        
        if criteria.get('max_carat'):
            where_parts.append("Carat <= ?")
            params.append(float(criteria['max_carat']))
        
        if criteria.get('color'):
            where_parts.append("Color = ?")
            params.append(criteria['color'])
        
        if criteria.get('clarity'):
            where_parts.append("Clarity = ?")
            params.append(criteria['clarity'])
        
        if criteria.get('cut'):
            where_parts.append("Cut = ?")
            params.append(criteria['cut'])
        
        where_clause = " AND ".join(where_parts)
        return self.get_all(where_clause, params)


class InventoryRepository(BaseRepository):
    """مستودع المخزون"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "Inventory")
    
    def get_by_diamond_id(self, diamond_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على مخزون الألماس"""
        results = self.get_all("DiamondID = ?", [diamond_id])
        return results[0] if results else None
    
    def get_available_inventory(self) -> List[Dict[str, Any]]:
        """الحصول على المخزون المتاح"""
        sql = """
        SELECT i.*, d.Carat, d.Color, d.Clarity, d.Cut, d.Shape, d.SellingPrice
        FROM Inventory i
        JOIN Diamonds d ON i.DiamondID = d.DiamondID
        WHERE i.Quantity > i.ReservedQuantity AND d.IsActive = 1
        """
        return self.execute_query(sql)
    
    def update_quantity(self, diamond_id: int, quantity_change: int) -> bool:
        """تحديث كمية المخزون"""
        sql = """
        UPDATE Inventory 
        SET Quantity = Quantity + ?, LastUpdated = ?
        WHERE DiamondID = ?
        """
        cursor = self.connection.cursor()
        cursor.execute(sql, [quantity_change, datetime.now().isoformat(), diamond_id])
        self.connection.commit()
        return cursor.rowcount > 0
    
    def reserve_quantity(self, diamond_id: int, quantity: int) -> bool:
        """حجز كمية من المخزون"""
        sql = """
        UPDATE Inventory 
        SET ReservedQuantity = ReservedQuantity + ?
        WHERE DiamondID = ? AND (Quantity - ReservedQuantity) >= ?
        """
        cursor = self.connection.cursor()
        cursor.execute(sql, [quantity, diamond_id, quantity])
        self.connection.commit()
        return cursor.rowcount > 0
    
    def release_reservation(self, diamond_id: int, quantity: int) -> bool:
        """إلغاء حجز كمية من المخزون"""
        sql = """
        UPDATE Inventory 
        SET ReservedQuantity = MAX(0, ReservedQuantity - ?)
        WHERE DiamondID = ?
        """
        cursor = self.connection.cursor()
        cursor.execute(sql, [quantity, diamond_id])
        self.connection.commit()
        return cursor.rowcount > 0


class SaleRepository(BaseRepository):
    """مستودع المبيعات"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "Sales")
    
    def get_sales_with_items(self, sale_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على المبيعات مع العناصر"""
        sale = self.get_by_id(sale_id)
        if not sale:
            return None
        
        # الحصول على عناصر المبيعات
        sql = """
        SELECT si.*, d.Carat, d.Color, d.Clarity, d.Cut, d.Shape
        FROM SaleItems si
        JOIN Diamonds d ON si.DiamondID = d.DiamondID
        WHERE si.SaleID = ?
        """
        items = self.execute_query(sql, [sale_id])
        sale['items'] = items
        
        return sale
    
    def get_sales_by_date_range(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """الحصول على المبيعات في فترة زمنية"""
        return self.get_all("SaleDate BETWEEN ? AND ?", [start_date.isoformat(), end_date.isoformat()])
    
    def get_customer_sales(self, customer_id: int) -> List[Dict[str, Any]]:
        """الحصول على مبيعات عميل"""
        return self.get_all("CustomerID = ?", [customer_id])


class SaleItemRepository(BaseRepository):
    """مستودع عناصر المبيعات"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "SaleItems")
    
    def get_by_sale_id(self, sale_id: int) -> List[Dict[str, Any]]:
        """الحصول على عناصر مبيعات"""
        return self.get_all("SaleID = ?", [sale_id])


class PurchaseRepository(BaseRepository):
    """مستودع المشتريات"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "Purchases")
    
    def get_purchases_with_items(self, purchase_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على المشتريات مع العناصر"""
        purchase = self.get_by_id(purchase_id)
        if not purchase:
            return None
        
        # الحصول على عناصر المشتريات
        sql = """
        SELECT pi.*, d.Carat, d.Color, d.Clarity, d.Cut, d.Shape
        FROM PurchaseItems pi
        JOIN Diamonds d ON pi.DiamondID = d.DiamondID
        WHERE pi.PurchaseID = ?
        """
        items = self.execute_query(sql, [purchase_id])
        purchase['items'] = items
        
        return purchase
    
    def get_purchases_by_date_range(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """الحصول على المشتريات في فترة زمنية"""
        return self.get_all("PurchaseDate BETWEEN ? AND ?", [start_date.isoformat(), end_date.isoformat()])
    
    def get_supplier_purchases(self, supplier_id: int) -> List[Dict[str, Any]]:
        """الحصول على مشتريات مورد"""
        return self.get_all("SupplierID = ?", [supplier_id])


class PurchaseItemRepository(BaseRepository):
    """مستودع عناصر المشتريات"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "PurchaseItems")
    
    def get_by_purchase_id(self, purchase_id: int) -> List[Dict[str, Any]]:
        """الحصول على عناصر مشتريات"""
        return self.get_all("PurchaseID = ?", [purchase_id])


class ReceiptRepository(BaseRepository):
    """مستودع سندات القبض"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "Receipts")
    
    def get_customer_receipts(self, customer_id: int) -> List[Dict[str, Any]]:
        """الحصول على سندات قبض عميل"""
        return self.get_all("CustomerID = ?", [customer_id])
    
    def get_receipts_by_date_range(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """الحصول على سندات القبض في فترة زمنية"""
        return self.get_all("ReceiptDate BETWEEN ? AND ?", [start_date.isoformat(), end_date.isoformat()])


class PaymentRepository(BaseRepository):
    """مستودع سندات الصرف"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "Payments")
    
    def get_supplier_payments(self, supplier_id: int) -> List[Dict[str, Any]]:
        """الحصول على سندات صرف مورد"""
        return self.get_all("SupplierID = ?", [supplier_id])
    
    def get_payments_by_date_range(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """الحصول على سندات الصرف في فترة زمنية"""
        return self.get_all("PaymentDate BETWEEN ? AND ?", [start_date.isoformat(), end_date.isoformat()])


class CashRepository(BaseRepository):
    """مستودع النقدية"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "Cash")
    
    def get_cash_balance_by_currency(self, currency_id: int) -> Decimal:
        """الحصول على رصيد النقدية بعملة معينة"""
        sql = """
        SELECT SUM(
            CASE 
                WHEN TransactionType IN ('RECEIPT', 'SALE') THEN Amount
                WHEN TransactionType IN ('PAYMENT', 'PURCHASE') THEN -Amount
                ELSE 0
            END
        ) as Balance
        FROM Cash
        WHERE CurrencyID = ?
        """
        result = self.execute_query(sql, [currency_id])
        balance = result[0]['Balance'] if result and result[0]['Balance'] else 0
        return Decimal(str(balance))
    
    def get_cash_transactions_by_date_range(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """الحصول على معاملات النقدية في فترة زمنية"""
        return self.get_all("TransactionDate BETWEEN ? AND ?", [start_date.isoformat(), end_date.isoformat()])


class ChartOfAccountRepository(BaseRepository):
    """مستودع الدليل المحاسبي"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "ChartOfAccounts")
    
    def get_by_code(self, account_code: str) -> Optional[Dict[str, Any]]:
        """الحصول على حساب بالرمز"""
        results = self.get_all("AccountCode = ?", [account_code])
        return results[0] if results else None
    
    def get_by_type(self, account_type: str) -> List[Dict[str, Any]]:
        """الحصول على الحسابات بالنوع"""
        return self.get_all("AccountType = ? AND IsActive = ?", [account_type, True])
    
    def get_parent_accounts(self) -> List[Dict[str, Any]]:
        """الحصول على الحسابات الرئيسية"""
        return self.get_all("ParentAccountID IS NULL AND IsActive = ?", [True])
    
    def get_child_accounts(self, parent_id: int) -> List[Dict[str, Any]]:
        """الحصول على الحسابات الفرعية"""
        return self.get_all("ParentAccountID = ? AND IsActive = ?", [parent_id, True])


class JournalEntryRepository(BaseRepository):
    """مستودع القيود اليومية"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "JournalEntries")
    
    def get_entry_with_details(self, entry_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على القيد مع التفاصيل"""
        entry = self.get_by_id(entry_id)
        if not entry:
            return None
        
        # الحصول على تفاصيل القيد
        sql = """
        SELECT jed.*, coa.AccountCode, coa.AccountName
        FROM JournalEntryDetails jed
        JOIN ChartOfAccounts coa ON jed.AccountID = coa.AccountID
        WHERE jed.EntryID = ?
        """
        details = self.execute_query(sql, [entry_id])
        entry['details'] = details
        
        return entry
    
    def get_entries_by_date_range(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """الحصول على القيود في فترة زمنية"""
        return self.get_all("EntryDate BETWEEN ? AND ?", [start_date.isoformat(), end_date.isoformat()])
    
    def get_unposted_entries(self) -> List[Dict[str, Any]]:
        """الحصول على القيود غير المرحلة"""
        return self.get_all("IsPosted = ?", [False])


class JournalEntryDetailRepository(BaseRepository):
    """مستودع تفاصيل القيود اليومية"""
    
    def __init__(self, connection: sqlite3.Connection):
        super().__init__(connection, "JournalEntryDetails")
    
    def get_by_entry_id(self, entry_id: int) -> List[Dict[str, Any]]:
        """الحصول على تفاصيل قيد"""
        return self.get_all("EntryID = ?", [entry_id])
    
    def get_account_transactions(self, account_id: int, start_date: date = None, end_date: date = None) -> List[Dict[str, Any]]:
        """الحصول على معاملات حساب"""
        sql = """
        SELECT jed.*, je.EntryDate, je.Description as EntryDescription
        FROM JournalEntryDetails jed
        JOIN JournalEntries je ON jed.EntryID = je.EntryID
        WHERE jed.AccountID = ?
        """
        params = [account_id]
        
        if start_date:
            sql += " AND je.EntryDate >= ?"
            params.append(start_date.isoformat())
        
        if end_date:
            sql += " AND je.EntryDate <= ?"
            params.append(end_date.isoformat())
        
        sql += " ORDER BY je.EntryDate, je.EntryID"
        
        return self.execute_query(sql, params)

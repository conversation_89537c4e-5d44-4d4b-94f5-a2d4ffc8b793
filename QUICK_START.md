# دليل البدء السريع - نظام إدارة مبيعات الألماس

## التشغيل السريع 🚀

### الطريقة الأسهل - تطبيق سطح المكتب
```bash
python desktop_app_simple.py
```

### أو استخدم ملف التشغيل السريع
```bash
# Windows
start.bat

# Linux/Mac
./start.sh
```

## الخطوات الأولى 📋

### 1. إضافة عميل جديد
- انقر على زر "إضافة عميل"
- أدخل اسم العميل
- أدخل رقم الهاتف (اختياري)
- انقر موافق

### 2. إضافة مورد جديد
- انقر على زر "إضافة مورد"
- أدخل اسم المورد
- أدخل رقم الهاتف (اختياري)
- انقر موافق

### 3. إضافة ألماس جديد
- انقر على زر "إضافة ألماس"
- أدخل القيراط (مثل: 1.5)
- أدخل اللون (مثل: D, E, F)
- أدخل النقاء (مثل: VVS1, VS1)
- أدخل سعر البيع بالدولار
- انقر موافق

### 4. عرض البيانات
- انقر على "عرض العملاء" لرؤية قائمة العملاء
- انقر على "عرض الموردين" لرؤية قائمة الموردين
- انقر على "عرض المخزون" لرؤية تفاصيل المخزون

## الميزات الرئيسية ⭐

### لوحة التحكم
- **الإحصائيات السريعة**: عدد العملاء، الموردين، قطع الألماس، وقيمة المخزون
- **جدول المخزون**: عرض مباشر للمخزون المتاح مع التفاصيل

### العمليات السريعة
- **إضافة سريعة**: أزرار مباشرة لإضافة العملاء والموردين والألماس
- **عرض البيانات**: أزرار لعرض جميع البيانات في نوافذ منفصلة
- **تحديث تلقائي**: تحديث البيانات تلقائياً بعد كل عملية

### إدارة المخزون
- **تتبع الكميات**: عرض الكميات المتاحة لكل قطعة ألماس
- **الأسعار**: عرض أسعار البيع لكل قطعة
- **التفاصيل**: عرض مواصفات الألماس (القيراط، اللون، النقاء)

## نصائح للاستخدام 💡

### 1. ترتيب البيانات
- أضف العملاء والموردين أولاً
- ثم أضف قطع الألماس
- استخدم أسماء واضحة ومفهومة

### 2. إدخال البيانات
- **القيراط**: استخدم الأرقام العشرية (مثل: 1.5, 2.0)
- **اللون**: استخدم الأحرف المعيارية (D, E, F, G, H, I, J)
- **النقاء**: استخدم المعايير المعيارية (FL, IF, VVS1, VVS2, VS1, VS2)
- **الأسعار**: أدخل الأسعار بالدولار الأمريكي

### 3. المتابعة
- انقر على "تحديث البيانات" لتحديث الإحصائيات
- راجع جدول المخزون بانتظام
- استخدم "عرض المخزون" لرؤية التفاصيل المالية

## حل المشاكل الشائعة 🔧

### المشكلة: التطبيق لا يفتح
**الحل**: تأكد من تثبيت Python 3.8+ وأن tkinter متوفر
```bash
python --version
python -c "import tkinter; print('tkinter متوفر')"
```

### المشكلة: خطأ في قاعدة البيانات
**الحل**: احذف ملف `diamonds_desktop.db` وأعد تشغيل التطبيق

### المشكلة: البيانات لا تظهر
**الحل**: انقر على زر "تحديث البيانات"

## الملفات المهمة 📁

- `desktop_app_simple.py` - التطبيق الرسومي الرئيسي
- `diamonds_desktop.db` - ملف قاعدة البيانات (ينشأ تلقائياً)
- `main.py` - الواجهة النصية البديلة
- `run_demo.py` - عرض توضيحي مع بيانات تجريبية

## الدعم والمساعدة 📞

### للمزيد من المعلومات
- اقرأ ملف `README.md` للتفاصيل الكاملة
- راجع ملف `PROJECT_SUMMARY.md` لملخص المشروع
- شغل `python run_demo.py` لرؤية عرض توضيحي

### اختبار النظام
```bash
python tests/test_system.py
```

---

**ملاحظة**: هذا النظام مطور للاستخدام المحلي ويحفظ البيانات في ملف SQLite محلي. تأكد من عمل نسخ احتياطية من ملف قاعدة البيانات بانتظام.

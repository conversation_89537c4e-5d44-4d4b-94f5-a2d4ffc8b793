"""
مدير المبيعات
"""

import sqlite3
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from decimal import Decimal

from models.base import Sale, SaleItem, Customer, Diamond
from database.repositories import (
    SaleRepository, SaleItemRepository, CustomerRepository, 
    DiamondRepository, InventoryRepository, CashRepository
)


class SalesManager:
    """مدير المبيعات"""
    
    def __init__(self, connection: sqlite3.Connection):
        """
        تهيئة مدير المبيعات
        
        Args:
            connection: اتصال قاعدة البيانات
        """
        self.connection = connection
        self.sale_repo = SaleRepository(connection)
        self.sale_item_repo = SaleItemRepository(connection)
        self.customer_repo = CustomerRepository(connection)
        self.diamond_repo = DiamondRepository(connection)
        self.inventory_repo = InventoryRepository(connection)
        self.cash_repo = CashRepository(connection)
    
    def create_sale(self, customer_id: Optional[int], sale_date: date, 
                   currency_id: int, notes: str = "") -> int:
        """
        إنشاء مبيعات جديدة
        
        Args:
            customer_id: معرف العميل
            sale_date: تاريخ المبيعات
            currency_id: معرف العملة
            notes: ملاحظات
            
        Returns:
            معرف المبيعات
        """
        sale_data = {
            'customer_id': customer_id,
            'sale_date': sale_date,
            'total_amount': Decimal('0'),
            'currency_id': currency_id,
            'payment_status': 'PENDING',
            'notes': notes,
            'created_at': datetime.now()
        }
        
        return self.sale_repo.insert(sale_data)
    
    def add_sale_item(self, sale_id: int, diamond_id: int, quantity: int, unit_price: Decimal) -> bool:
        """
        إضافة عنصر إلى المبيعات
        
        Args:
            sale_id: معرف المبيعات
            diamond_id: معرف الألماس
            quantity: الكمية
            unit_price: سعر الوحدة
            
        Returns:
            True إذا تمت الإضافة بنجاح
        """
        # التحقق من توفر المخزون
        inventory = self.inventory_repo.get_by_diamond_id(diamond_id)
        if not inventory or inventory['available_quantity'] < quantity:
            raise ValueError("الكمية المطلوبة غير متوفرة في المخزون")
        
        # حساب السعر الإجمالي
        total_price = unit_price * quantity
        
        # إضافة عنصر المبيعات
        item_data = {
            'sale_id': sale_id,
            'diamond_id': diamond_id,
            'quantity': quantity,
            'unit_price': unit_price,
            'total_price': total_price
        }
        
        item_id = self.sale_item_repo.insert(item_data)
        
        if item_id:
            # حجز الكمية من المخزون
            self.inventory_repo.reserve_quantity(diamond_id, quantity)
            
            # تحديث إجمالي المبيعات
            self._update_sale_total(sale_id)
            
            return True
        
        return False
    
    def remove_sale_item(self, sale_item_id: int) -> bool:
        """
        إزالة عنصر من المبيعات
        
        Args:
            sale_item_id: معرف عنصر المبيعات
            
        Returns:
            True إذا تمت الإزالة بنجاح
        """
        # الحصول على بيانات العنصر
        item = self.sale_item_repo.get_by_id(sale_item_id, 'sale_item_id')
        if not item:
            return False
        
        sale_id = item['sale_id']
        diamond_id = item['diamond_id']
        quantity = item['quantity']
        
        # حذف العنصر
        if self.sale_item_repo.delete(sale_item_id, 'sale_item_id'):
            # إلغاء حجز الكمية
            self.inventory_repo.release_reservation(diamond_id, quantity)
            
            # تحديث إجمالي المبيعات
            self._update_sale_total(sale_id)
            
            return True
        
        return False
    
    def finalize_sale(self, sale_id: int) -> bool:
        """
        إنهاء المبيعات وتحديث المخزون
        
        Args:
            sale_id: معرف المبيعات
            
        Returns:
            True إذا تم الإنهاء بنجاح
        """
        # الحصول على المبيعات مع العناصر
        sale = self.sale_repo.get_sales_with_items(sale_id)
        if not sale or not sale.get('items'):
            return False
        
        try:
            # بدء معاملة قاعدة البيانات
            self.connection.execute("BEGIN TRANSACTION")
            
            # تحديث المخزون لكل عنصر
            for item in sale['items']:
                diamond_id = item['diamond_id']
                quantity = item['quantity']
                
                # تقليل الكمية من المخزون
                self.inventory_repo.update_quantity(diamond_id, -quantity)
                
                # إلغاء الحجز
                self.inventory_repo.release_reservation(diamond_id, quantity)
            
            # تحديث حالة الدفع
            self.sale_repo.update(sale_id, {'payment_status': 'COMPLETED'})
            
            # إضافة معاملة نقدية
            cash_data = {
                'amount': sale['total_amount'],
                'currency_id': sale['currency_id'],
                'transaction_date': date.today(),
                'transaction_type': 'SALE',
                'description': f"مبيعات رقم {sale_id}",
                'reference_id': sale_id,
                'reference_type': 'SALE'
            }
            self.cash_repo.insert(cash_data)
            
            # تأكيد المعاملة
            self.connection.commit()
            return True
            
        except Exception as e:
            # إلغاء المعاملة في حالة الخطأ
            self.connection.rollback()
            raise e
    
    def cancel_sale(self, sale_id: int) -> bool:
        """
        إلغاء المبيعات
        
        Args:
            sale_id: معرف المبيعات
            
        Returns:
            True إذا تم الإلغاء بنجاح
        """
        # الحصول على المبيعات مع العناصر
        sale = self.sale_repo.get_sales_with_items(sale_id)
        if not sale:
            return False
        
        try:
            # بدء معاملة قاعدة البيانات
            self.connection.execute("BEGIN TRANSACTION")
            
            # إلغاء حجز المخزون لكل عنصر
            if sale.get('items'):
                for item in sale['items']:
                    diamond_id = item['diamond_id']
                    quantity = item['quantity']
                    self.inventory_repo.release_reservation(diamond_id, quantity)
            
            # تحديث حالة المبيعات
            self.sale_repo.update(sale_id, {'payment_status': 'CANCELLED'})
            
            # تأكيد المعاملة
            self.connection.commit()
            return True
            
        except Exception as e:
            # إلغاء المعاملة في حالة الخطأ
            self.connection.rollback()
            raise e
    
    def get_sale_details(self, sale_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على تفاصيل المبيعات
        
        Args:
            sale_id: معرف المبيعات
            
        Returns:
            تفاصيل المبيعات
        """
        return self.sale_repo.get_sales_with_items(sale_id)
    
    def get_sales_by_date_range(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """
        الحصول على المبيعات في فترة زمنية
        
        Args:
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            قائمة المبيعات
        """
        return self.sale_repo.get_sales_by_date_range(start_date, end_date)
    
    def get_customer_sales(self, customer_id: int) -> List[Dict[str, Any]]:
        """
        الحصول على مبيعات عميل
        
        Args:
            customer_id: معرف العميل
            
        Returns:
            قائمة مبيعات العميل
        """
        return self.sale_repo.get_customer_sales(customer_id)
    
    def get_sales_summary(self, start_date: date, end_date: date) -> Dict[str, Any]:
        """
        الحصول على ملخص المبيعات
        
        Args:
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            ملخص المبيعات
        """
        sales = self.get_sales_by_date_range(start_date, end_date)
        
        total_sales = len(sales)
        total_amount = sum(Decimal(str(sale['total_amount'])) for sale in sales)
        completed_sales = len([s for s in sales if s['payment_status'] == 'COMPLETED'])
        pending_sales = len([s for s in sales if s['payment_status'] == 'PENDING'])
        
        return {
            'total_sales': total_sales,
            'total_amount': total_amount,
            'completed_sales': completed_sales,
            'pending_sales': pending_sales,
            'average_sale_amount': total_amount / total_sales if total_sales > 0 else Decimal('0')
        }
    
    def _update_sale_total(self, sale_id: int):
        """
        تحديث إجمالي المبيعات
        
        Args:
            sale_id: معرف المبيعات
        """
        items = self.sale_item_repo.get_by_sale_id(sale_id)
        total_amount = sum(Decimal(str(item['total_price'])) for item in items)
        
        self.sale_repo.update(sale_id, {'total_amount': total_amount})

@echo off
echo ========================================
echo    نظام إدارة مبيعات الألماس
echo ========================================
echo.
echo اختر طريقة التشغيل:
echo 1. تطبيق سطح المكتب (واجهة رسومية)
echo 2. الواجهة النصية
echo 3. العرض التوضيحي
echo 4. الاختبارات
echo 5. خروج
echo.
set /p choice="أدخل اختيارك (1-5): "

if "%choice%"=="1" (
    echo تشغيل تطبيق سطح المكتب...
    python desktop_app_simple.py
) else if "%choice%"=="2" (
    echo تشغيل الواجهة النصية...
    python main.py
) else if "%choice%"=="3" (
    echo تشغيل العرض التوضيحي...
    python run_demo.py
    pause
) else if "%choice%"=="4" (
    echo تشغيل الاختبارات...
    python tests/test_system.py
    pause
) else if "%choice%"=="5" (
    echo وداعاً!
    exit
) else (
    echo خيار غير صحيح!
    pause
)

pause

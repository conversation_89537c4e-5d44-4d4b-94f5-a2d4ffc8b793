#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق سطح المكتب المبسط لنظام إدارة مبيعات الألماس
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import date, datetime
from decimal import Decimal

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.schema import DatabaseSchema
from database.repositories import *
from business.sales_manager import SalesManager
from business.purchase_manager import PurchaseManager
from business.inventory_manager import InventoryManager
from business.currency_manager import CurrencyManager
from business.accounting_manager import AccountingManager


class SimpleDiamondApp:
    """تطبيق مبسط لإدارة مبيعات الألماس"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.root = tk.Tk()
        self.root.title("نظام إدارة مبيعات الألماس - الإصدار المبسط")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # تهيئة قاعدة البيانات
        self.setup_database()
        
        # تهيئة المدراء
        self.setup_managers()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحديث البيانات
        self.refresh_data()
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            self.db_schema = DatabaseSchema("diamonds_desktop.db")
            self.connection = self.db_schema.connect()
            
            # إنشاء الجداول إذا لم تكن موجودة
            cursor = self.connection.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            if not tables:
                self.db_schema.create_tables()
                self.db_schema.insert_default_data()
                messagebox.showinfo("نجح", "تم إنشاء قاعدة البيانات بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قاعدة البيانات: {e}")
            self.root.quit()
    
    def setup_managers(self):
        """تهيئة المدراء"""
        self.sales_manager = SalesManager(self.connection)
        self.purchase_manager = PurchaseManager(self.connection)
        self.inventory_manager = InventoryManager(self.connection)
        self.currency_manager = CurrencyManager(self.connection)
        self.accounting_manager = AccountingManager(self.connection)
        
        # تهيئة المستودعات
        self.customer_repo = CustomerRepository(self.connection)
        self.supplier_repo = SupplierRepository(self.connection)
        self.diamond_repo = DiamondRepository(self.connection)
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = tk.Label(self.root, text="نظام إدارة مبيعات الألماس", 
                              font=("Arial", 16, "bold"), bg='#f0f0f0')
        title_label.pack(pady=10)
        
        # إطار الإحصائيات
        stats_frame = ttk.LabelFrame(self.root, text="إحصائيات سريعة")
        stats_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # إحصائيات في صف واحد
        stats_row = ttk.Frame(stats_frame)
        stats_row.pack(fill=tk.X, padx=10, pady=10)
        
        # عدد العملاء
        ttk.Label(stats_row, text="العملاء:", font=("Arial", 10, "bold")).grid(row=0, column=0, padx=5)
        self.customers_count_label = ttk.Label(stats_row, text="0", font=("Arial", 12))
        self.customers_count_label.grid(row=0, column=1, padx=5)
        
        # عدد الموردين
        ttk.Label(stats_row, text="الموردين:", font=("Arial", 10, "bold")).grid(row=0, column=2, padx=5)
        self.suppliers_count_label = ttk.Label(stats_row, text="0", font=("Arial", 12))
        self.suppliers_count_label.grid(row=0, column=3, padx=5)
        
        # عدد قطع الألماس
        ttk.Label(stats_row, text="قطع الألماس:", font=("Arial", 10, "bold")).grid(row=0, column=4, padx=5)
        self.diamonds_count_label = ttk.Label(stats_row, text="0", font=("Arial", 12))
        self.diamonds_count_label.grid(row=0, column=5, padx=5)
        
        # قيمة المخزون
        ttk.Label(stats_row, text="قيمة المخزون:", font=("Arial", 10, "bold")).grid(row=0, column=6, padx=5)
        self.inventory_value_label = ttk.Label(stats_row, text="$0", font=("Arial", 12))
        self.inventory_value_label.grid(row=0, column=7, padx=5)
        
        # إطار الأزرار
        buttons_frame = ttk.LabelFrame(self.root, text="العمليات السريعة")
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # صف الأزرار الأول
        buttons_row1 = ttk.Frame(buttons_frame)
        buttons_row1.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(buttons_row1, text="إضافة عميل", command=self.add_customer_simple).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_row1, text="إضافة مورد", command=self.add_supplier_simple).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_row1, text="إضافة ألماس", command=self.add_diamond_simple).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_row1, text="تحديث البيانات", command=self.refresh_data).pack(side=tk.LEFT, padx=5)
        
        # صف الأزرار الثاني
        buttons_row2 = ttk.Frame(buttons_frame)
        buttons_row2.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(buttons_row2, text="عرض العملاء", command=self.show_customers).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_row2, text="عرض الموردين", command=self.show_suppliers).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_row2, text="عرض المخزون", command=self.show_inventory).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_row2, text="حول البرنامج", command=self.show_about).pack(side=tk.LEFT, padx=5)
        
        # إطار المخزون
        inventory_frame = ttk.LabelFrame(self.root, text="المخزون المتاح")
        inventory_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # جدول المخزون
        columns = ("المعرف", "القيراط", "اللون", "النقاء", "الكمية", "السعر")
        self.inventory_tree = ttk.Treeview(inventory_frame, columns=columns, show="headings", height=12)
        
        for col in columns:
            self.inventory_tree.heading(col, text=col)
            self.inventory_tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(inventory_frame, orient=tk.VERTICAL, command=self.inventory_tree.yview)
        self.inventory_tree.configure(yscrollcommand=scrollbar.set)
        
        self.inventory_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # شريط الحالة
        self.status_bar = ttk.Label(self.root, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def refresh_data(self):
        """تحديث جميع البيانات"""
        try:
            self.update_stats()
            self.load_inventory()
            self.status_bar.config(text="تم تحديث البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث البيانات: {e}")
    
    def update_stats(self):
        """تحديث الإحصائيات"""
        try:
            # عدد العملاء
            customers = self.customer_repo.get_active_customers()
            self.customers_count_label.config(text=str(len(customers)))
            
            # عدد الموردين
            suppliers = self.supplier_repo.get_active_suppliers()
            self.suppliers_count_label.config(text=str(len(suppliers)))
            
            # عدد قطع الألماس
            diamonds = self.diamond_repo.get_active_diamonds()
            self.diamonds_count_label.config(text=str(len(diamonds)))
            
            # قيمة المخزون
            inventory_value = self.inventory_manager.get_inventory_value()
            self.inventory_value_label.config(text=f"${inventory_value['total_selling_value']:,.2f}")
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    def load_inventory(self):
        """تحميل بيانات المخزون"""
        try:
            # مسح البيانات الحالية
            for item in self.inventory_tree.get_children():
                self.inventory_tree.delete(item)
            
            # تحميل المخزون المتاح
            inventory = self.inventory_manager.get_available_inventory()
            
            for item in inventory:
                self.inventory_tree.insert("", "end", values=(
                    item.get('DiamondID', ''),
                    item.get('Carat', ''),
                    item.get('Color', ''),
                    item.get('Clarity', ''),
                    item.get('Quantity', ''),
                    f"${item.get('SellingPrice', 0):,.2f}"
                ))
        except Exception as e:
            print(f"خطأ في تحميل المخزون: {e}")
    
    def add_customer_simple(self):
        """إضافة عميل بطريقة مبسطة"""
        name = tk.simpledialog.askstring("إضافة عميل", "اسم العميل:")
        if name:
            phone = tk.simpledialog.askstring("إضافة عميل", "رقم الهاتف (اختياري):") or ""
            try:
                customer_data = {
                    'Name': name,
                    'Phone': phone,
                    'Email': '',
                    'Address': '',
                    'ContactInfo': '',
                    'IsActive': True,
                    'CreatedAt': datetime.now()
                }
                
                customer_id = self.customer_repo.insert(customer_data)
                messagebox.showinfo("نجح", f"تم إضافة العميل بنجاح! المعرف: {customer_id}")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إضافة العميل: {e}")
    
    def add_supplier_simple(self):
        """إضافة مورد بطريقة مبسطة"""
        name = tk.simpledialog.askstring("إضافة مورد", "اسم المورد:")
        if name:
            phone = tk.simpledialog.askstring("إضافة مورد", "رقم الهاتف (اختياري):") or ""
            try:
                supplier_data = {
                    'Name': name,
                    'Phone': phone,
                    'Email': '',
                    'Address': '',
                    'ContactInfo': '',
                    'IsActive': True,
                    'CreatedAt': datetime.now()
                }
                
                supplier_id = self.supplier_repo.insert(supplier_data)
                messagebox.showinfo("نجح", f"تم إضافة المورد بنجاح! المعرف: {supplier_id}")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إضافة المورد: {e}")
    
    def add_diamond_simple(self):
        """إضافة ألماس بطريقة مبسطة"""
        carat = tk.simpledialog.askfloat("إضافة ألماس", "القيراط:")
        if carat:
            color = tk.simpledialog.askstring("إضافة ألماس", "اللون (مثل D, E, F):") or "G"
            clarity = tk.simpledialog.askstring("إضافة ألماس", "النقاء (مثل VVS1, VS1):") or "VS1"
            selling_price = tk.simpledialog.askfloat("إضافة ألماس", "سعر البيع ($):") or 1000
            
            try:
                # الحصول على العملة الافتراضية
                base_currency = self.currency_manager.get_base_currency()
                currency_id = base_currency['CurrencyID'] if base_currency else 1
                
                diamond_id = self.inventory_manager.add_diamond(
                    carat=Decimal(str(carat)),
                    color=color,
                    clarity=clarity,
                    cut="Good",
                    shape="Round",
                    certificate_number="",
                    cost_price=Decimal(str(selling_price * 0.7)),  # تقدير سعر التكلفة
                    selling_price=Decimal(str(selling_price)),
                    currency_id=currency_id,
                    description=""
                )
                
                messagebox.showinfo("نجح", f"تم إضافة الألماس بنجاح! المعرف: {diamond_id}")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إضافة الألماس: {e}")
    
    def show_customers(self):
        """عرض العملاء"""
        try:
            customers = self.customer_repo.get_active_customers()
            
            # إنشاء نافذة جديدة
            window = tk.Toplevel(self.root)
            window.title("قائمة العملاء")
            window.geometry("600x400")
            
            # جدول العملاء
            columns = ("المعرف", "الاسم", "الهاتف")
            tree = ttk.Treeview(window, columns=columns, show="headings")
            
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=150)
            
            for customer in customers:
                tree.insert("", "end", values=(
                    customer.get('CustomerID', ''),
                    customer.get('Name', ''),
                    customer.get('Phone', '')
                ))
            
            tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض العملاء: {e}")
    
    def show_suppliers(self):
        """عرض الموردين"""
        try:
            suppliers = self.supplier_repo.get_active_suppliers()
            
            # إنشاء نافذة جديدة
            window = tk.Toplevel(self.root)
            window.title("قائمة الموردين")
            window.geometry("600x400")
            
            # جدول الموردين
            columns = ("المعرف", "الاسم", "الهاتف")
            tree = ttk.Treeview(window, columns=columns, show="headings")
            
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=150)
            
            for supplier in suppliers:
                tree.insert("", "end", values=(
                    supplier.get('SupplierID', ''),
                    supplier.get('Name', ''),
                    supplier.get('Phone', '')
                ))
            
            tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض الموردين: {e}")
    
    def show_inventory(self):
        """عرض تفاصيل المخزون"""
        try:
            inventory_value = self.inventory_manager.get_inventory_value()
            
            info_text = f"""
تفاصيل المخزون:

إجمالي عدد القطع: {inventory_value['total_items']}
إجمالي الكمية: {inventory_value['total_quantity']}
قيمة التكلفة الإجمالية: ${inventory_value['total_cost_value']:,.2f}
قيمة البيع الإجمالية: ${inventory_value['total_selling_value']:,.2f}
الربح المتوقع: ${inventory_value['total_selling_value'] - inventory_value['total_cost_value']:,.2f}
            """
            
            messagebox.showinfo("تفاصيل المخزون", info_text)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض المخزون: {e}")
    
    def show_about(self):
        """حول البرنامج"""
        about_text = """
نظام إدارة مبيعات الألماس
الإصدار المبسط 1.0

نظام شامل لإدارة مبيعات الألماس يدعم:
• إدارة العملاء والموردين
• إدارة المخزون والألماس
• عمليات البيع والشراء
• النظام المحاسبي
• دعم العملات المتعددة

تم التطوير بلغة Python مع tkinter
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()
        
    def __del__(self):
        """تنظيف الموارد"""
        if hasattr(self, 'db_schema'):
            self.db_schema.close()


if __name__ == "__main__":
    try:
        # استيراد simpledialog
        import tkinter.simpledialog
        tk.simpledialog = tkinter.simpledialog
        
        app = SimpleDiamondApp()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        try:
            messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{e}")
        except:
            pass

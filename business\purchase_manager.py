"""
مدير المشتريات
"""

import sqlite3
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from decimal import Decimal

from models.base import Purchase, PurchaseItem, Supplier, Diamond
from database.repositories import (
    PurchaseRepository, PurchaseItemRepository, SupplierRepository, 
    DiamondRepository, InventoryRepository, CashRepository
)


class PurchaseManager:
    """مدير المشتريات"""
    
    def __init__(self, connection: sqlite3.Connection):
        """
        تهيئة مدير المشتريات
        
        Args:
            connection: اتصال قاعدة البيانات
        """
        self.connection = connection
        self.purchase_repo = PurchaseRepository(connection)
        self.purchase_item_repo = PurchaseItemRepository(connection)
        self.supplier_repo = SupplierRepository(connection)
        self.diamond_repo = DiamondRepository(connection)
        self.inventory_repo = InventoryRepository(connection)
        self.cash_repo = CashRepository(connection)
    
    def create_purchase(self, supplier_id: Optional[int], purchase_date: date, 
                       currency_id: int, notes: str = "") -> int:
        """
        إنشاء مشتريات جديدة
        
        Args:
            supplier_id: معرف المورد
            purchase_date: تاريخ المشتريات
            currency_id: معرف العملة
            notes: ملاحظات
            
        Returns:
            معرف المشتريات
        """
        purchase_data = {
            'SupplierID': supplier_id,
            'PurchaseDate': purchase_date,
            'TotalAmount': Decimal('0'),
            'CurrencyID': currency_id,
            'PaymentStatus': 'PENDING',
            'Notes': notes,
            'CreatedAt': datetime.now()
        }
        
        return self.purchase_repo.insert(purchase_data)
    
    def add_purchase_item(self, purchase_id: int, diamond_id: int, quantity: int, unit_price: Decimal) -> bool:
        """
        إضافة عنصر إلى المشتريات
        
        Args:
            purchase_id: معرف المشتريات
            diamond_id: معرف الألماس
            quantity: الكمية
            unit_price: سعر الوحدة
            
        Returns:
            True إذا تمت الإضافة بنجاح
        """
        # حساب السعر الإجمالي
        total_price = unit_price * quantity
        
        # إضافة عنصر المشتريات
        item_data = {
            'PurchaseID': purchase_id,
            'DiamondID': diamond_id,
            'Quantity': quantity,
            'UnitPrice': unit_price,
            'TotalPrice': total_price
        }
        
        item_id = self.purchase_item_repo.insert(item_data)
        
        if item_id:
            # تحديث إجمالي المشتريات
            self._update_purchase_total(purchase_id)
            return True
        
        return False
    
    def remove_purchase_item(self, purchase_item_id: int) -> bool:
        """
        إزالة عنصر من المشتريات
        
        Args:
            purchase_item_id: معرف عنصر المشتريات
            
        Returns:
            True إذا تمت الإزالة بنجاح
        """
        # الحصول على بيانات العنصر
        item = self.purchase_item_repo.get_by_id(purchase_item_id, 'purchase_item_id')
        if not item:
            return False
        
        purchase_id = item['PurchaseID']
        
        # حذف العنصر
        if self.purchase_item_repo.delete(purchase_item_id, 'purchase_item_id'):
            # تحديث إجمالي المشتريات
            self._update_purchase_total(purchase_id)
            return True
        
        return False
    
    def finalize_purchase(self, purchase_id: int) -> bool:
        """
        إنهاء المشتريات وتحديث المخزون
        
        Args:
            purchase_id: معرف المشتريات
            
        Returns:
            True إذا تم الإنهاء بنجاح
        """
        # الحصول على المشتريات مع العناصر
        purchase = self.purchase_repo.get_purchases_with_items(purchase_id)
        if not purchase or not purchase.get('items'):
            return False
        
        try:
            # بدء معاملة قاعدة البيانات
            self.connection.execute("BEGIN TRANSACTION")
            
            # تحديث المخزون لكل عنصر
            for item in purchase['items']:
                diamond_id = item['DiamondID']
                quantity = item['Quantity']
                unit_price = item['UnitPrice']

                # إضافة الكمية إلى المخزون
                self.inventory_repo.update_quantity(diamond_id, quantity)

                # تحديث سعر التكلفة للألماس
                self._update_diamond_cost_price(diamond_id, unit_price)

            # تحديث حالة الدفع
            self.purchase_repo.update(purchase_id, {'PaymentStatus': 'COMPLETED'})
            
            # إضافة معاملة نقدية
            cash_data = {
                'Amount': -purchase['TotalAmount'],  # سالب لأنه صرف
                'CurrencyID': purchase['CurrencyID'],
                'TransactionDate': date.today(),
                'TransactionType': 'PURCHASE',
                'Description': f"مشتريات رقم {purchase_id}",
                'ReferenceID': purchase_id,
                'ReferenceType': 'PURCHASE'
            }
            self.cash_repo.insert(cash_data)
            
            # تأكيد المعاملة
            self.connection.commit()
            return True
            
        except Exception as e:
            # إلغاء المعاملة في حالة الخطأ
            self.connection.rollback()
            raise e
    
    def cancel_purchase(self, purchase_id: int) -> bool:
        """
        إلغاء المشتريات
        
        Args:
            purchase_id: معرف المشتريات
            
        Returns:
            True إذا تم الإلغاء بنجاح
        """
        # تحديث حالة المشتريات
        return self.purchase_repo.update(purchase_id, {'PaymentStatus': 'CANCELLED'})
    
    def get_purchase_details(self, purchase_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على تفاصيل المشتريات
        
        Args:
            purchase_id: معرف المشتريات
            
        Returns:
            تفاصيل المشتريات
        """
        return self.purchase_repo.get_purchases_with_items(purchase_id)
    
    def get_purchases_by_date_range(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """
        الحصول على المشتريات في فترة زمنية
        
        Args:
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            قائمة المشتريات
        """
        return self.purchase_repo.get_purchases_by_date_range(start_date, end_date)
    
    def get_supplier_purchases(self, supplier_id: int) -> List[Dict[str, Any]]:
        """
        الحصول على مشتريات مورد
        
        Args:
            supplier_id: معرف المورد
            
        Returns:
            قائمة مشتريات المورد
        """
        return self.purchase_repo.get_supplier_purchases(supplier_id)
    
    def get_purchases_summary(self, start_date: date, end_date: date) -> Dict[str, Any]:
        """
        الحصول على ملخص المشتريات
        
        Args:
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            ملخص المشتريات
        """
        purchases = self.get_purchases_by_date_range(start_date, end_date)
        
        total_purchases = len(purchases)
        total_amount = sum(Decimal(str(purchase['total_amount'])) for purchase in purchases)
        completed_purchases = len([p for p in purchases if p['payment_status'] == 'COMPLETED'])
        pending_purchases = len([p for p in purchases if p['payment_status'] == 'PENDING'])
        
        return {
            'total_purchases': total_purchases,
            'total_amount': total_amount,
            'completed_purchases': completed_purchases,
            'pending_purchases': pending_purchases,
            'average_purchase_amount': total_amount / total_purchases if total_purchases > 0 else Decimal('0')
        }
    
    def _update_purchase_total(self, purchase_id: int):
        """
        تحديث إجمالي المشتريات
        
        Args:
            purchase_id: معرف المشتريات
        """
        items = self.purchase_item_repo.get_by_purchase_id(purchase_id)
        total_amount = sum(Decimal(str(item['TotalPrice'])) for item in items)

        self.purchase_repo.update(purchase_id, {'TotalAmount': total_amount})
    
    def _update_diamond_cost_price(self, diamond_id: int, new_cost_price: Decimal):
        """
        تحديث سعر تكلفة الألماس
        
        Args:
            diamond_id: معرف الألماس
            new_cost_price: سعر التكلفة الجديد
        """
        # يمكن تطوير منطق أكثر تعقيداً هنا لحساب متوسط التكلفة
        self.diamond_repo.update(diamond_id, {'CostPrice': new_cost_price})

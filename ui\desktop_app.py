"""
واجهة سطح المكتب لنظام إدارة مبيعات الألماس
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sys
import os
from datetime import date, datetime
from decimal import Decimal
from typing import Optional, List, Dict, Any

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.schema import DatabaseSchema
from database.repositories import *
from business.sales_manager import SalesManager
from business.purchase_manager import PurchaseManager
from business.inventory_manager import InventoryManager
from business.currency_manager import CurrencyManager
from business.accounting_manager import AccountingManager
try:
    from ui.dialogs import CustomerDialog, SupplierDialog, DiamondDialog, SaleDialog, PurchaseDialog
except ImportError:
    from dialogs import CustomerDialog, SupplierDialog, DiamondDialog, SaleDialog, PurchaseDialog


class DiamondManagementApp:
    """تطبيق إدارة مبيعات الألماس لسطح المكتب"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.root = tk.Tk()
        self.root.title("نظام إدارة مبيعات الألماس")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # تهيئة قاعدة البيانات
        self.setup_database()
        
        # تهيئة المدراء
        self.setup_managers()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحديث البيانات
        self.refresh_data()
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        self.db_schema = DatabaseSchema("diamonds_desktop.db")
        self.connection = self.db_schema.connect()
        
        # إنشاء الجداول إذا لم تكن موجودة
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            if not tables:
                self.db_schema.create_tables()
                self.db_schema.insert_default_data()
                messagebox.showinfo("نجح", "تم إنشاء قاعدة البيانات بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قاعدة البيانات: {e}")
    
    def setup_managers(self):
        """تهيئة المدراء"""
        self.sales_manager = SalesManager(self.connection)
        self.purchase_manager = PurchaseManager(self.connection)
        self.inventory_manager = InventoryManager(self.connection)
        self.currency_manager = CurrencyManager(self.connection)
        self.accounting_manager = AccountingManager(self.connection)
        
        # تهيئة المستودعات
        self.customer_repo = CustomerRepository(self.connection)
        self.supplier_repo = SupplierRepository(self.connection)
        self.diamond_repo = DiamondRepository(self.connection)
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء القائمة العلوية
        self.create_menu()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء النافذة الرئيسية
        self.create_main_frame()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_menu(self):
        """إنشاء القائمة العلوية"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_database)
        file_menu.add_command(label="فتح", command=self.open_database)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة البيانات
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="البيانات", menu=data_menu)
        data_menu.add_command(label="العملاء", command=self.show_customers)
        data_menu.add_command(label="الموردين", command=self.show_suppliers)
        data_menu.add_command(label="الألماس", command=self.show_diamonds)
        
        # قائمة العمليات
        operations_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="العمليات", menu=operations_menu)
        operations_menu.add_command(label="مبيعات جديدة", command=self.new_sale)
        operations_menu.add_command(label="مشتريات جديدة", command=self.new_purchase)
        operations_menu.add_command(label="سند قبض", command=self.new_receipt)
        operations_menu.add_command(label="سند صرف", command=self.new_payment)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير المخزون", command=self.inventory_report)
        reports_menu.add_command(label="تقرير المبيعات", command=self.sales_report)
        reports_menu.add_command(label="تقرير المشتريات", command=self.purchases_report)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        # أزرار سريعة
        ttk.Button(toolbar, text="عميل جديد", command=self.add_customer).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="مورد جديد", command=self.add_supplier).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="ألماس جديد", command=self.add_diamond).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        ttk.Button(toolbar, text="مبيعات", command=self.new_sale).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="مشتريات", command=self.new_purchase).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        ttk.Button(toolbar, text="تحديث", command=self.refresh_data).pack(side=tk.LEFT, padx=2)
        
        # معلومات سريعة
        info_frame = ttk.Frame(toolbar)
        info_frame.pack(side=tk.RIGHT, padx=10)
        
        self.info_label = ttk.Label(info_frame, text="جاري التحميل...")
        self.info_label.pack()
    
    def create_main_frame(self):
        """إنشاء النافذة الرئيسية"""
        # إنشاء Notebook للتبويبات
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # تبويب لوحة التحكم
        self.create_dashboard_tab()
        
        # تبويب العملاء
        self.create_customers_tab()
        
        # تبويب الموردين
        self.create_suppliers_tab()
        
        # تبويب الألماس
        self.create_diamonds_tab()
        
        # تبويب المبيعات
        self.create_sales_tab()
        
        # تبويب المشتريات
        self.create_purchases_tab()
    
    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="لوحة التحكم")
        
        # إحصائيات سريعة
        stats_frame = ttk.LabelFrame(dashboard_frame, text="إحصائيات سريعة")
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # إنشاء إطارات للإحصائيات
        stats_row1 = ttk.Frame(stats_frame)
        stats_row1.pack(fill=tk.X, padx=5, pady=5)
        
        # عدد العملاء
        customers_frame = ttk.Frame(stats_row1)
        customers_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        ttk.Label(customers_frame, text="العملاء", font=("Arial", 12, "bold")).pack()
        self.customers_count_label = ttk.Label(customers_frame, text="0", font=("Arial", 16))
        self.customers_count_label.pack()
        
        # عدد الموردين
        suppliers_frame = ttk.Frame(stats_row1)
        suppliers_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        ttk.Label(suppliers_frame, text="الموردين", font=("Arial", 12, "bold")).pack()
        self.suppliers_count_label = ttk.Label(suppliers_frame, text="0", font=("Arial", 16))
        self.suppliers_count_label.pack()
        
        # عدد قطع الألماس
        diamonds_frame = ttk.Frame(stats_row1)
        diamonds_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        ttk.Label(diamonds_frame, text="قطع الألماس", font=("Arial", 12, "bold")).pack()
        self.diamonds_count_label = ttk.Label(diamonds_frame, text="0", font=("Arial", 16))
        self.diamonds_count_label.pack()
        
        # قيمة المخزون
        inventory_frame = ttk.Frame(stats_row1)
        inventory_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        ttk.Label(inventory_frame, text="قيمة المخزون", font=("Arial", 12, "bold")).pack()
        self.inventory_value_label = ttk.Label(inventory_frame, text="$0", font=("Arial", 16))
        self.inventory_value_label.pack()
        
        # المخزون المتاح
        available_inventory_frame = ttk.LabelFrame(dashboard_frame, text="المخزون المتاح")
        available_inventory_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # جدول المخزون
        columns = ("المعرف", "القيراط", "اللون", "النقاء", "الكمية", "السعر")
        self.inventory_tree = ttk.Treeview(available_inventory_frame, columns=columns, show="headings", height=10)
        
        for col in columns:
            self.inventory_tree.heading(col, text=col)
            self.inventory_tree.column(col, width=100)
        
        # شريط التمرير
        inventory_scrollbar = ttk.Scrollbar(available_inventory_frame, orient=tk.VERTICAL, command=self.inventory_tree.yview)
        self.inventory_tree.configure(yscrollcommand=inventory_scrollbar.set)
        
        self.inventory_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        inventory_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_customers_tab(self):
        """إنشاء تبويب العملاء"""
        customers_frame = ttk.Frame(self.notebook)
        self.notebook.add(customers_frame, text="العملاء")
        
        # أزرار العملاء
        buttons_frame = ttk.Frame(customers_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="إضافة عميل", command=self.add_customer).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="تعديل عميل", command=self.edit_customer).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="حذف عميل", command=self.delete_customer).pack(side=tk.LEFT, padx=5)
        
        # جدول العملاء
        columns = ("المعرف", "الاسم", "الهاتف", "البريد الإلكتروني", "العنوان")
        self.customers_tree = ttk.Treeview(customers_frame, columns=columns, show="headings")
        
        for col in columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=150)
        
        # شريط التمرير للعملاء
        customers_scrollbar = ttk.Scrollbar(customers_frame, orient=tk.VERTICAL, command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=customers_scrollbar.set)
        
        self.customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        customers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def create_suppliers_tab(self):
        """إنشاء تبويب الموردين"""
        suppliers_frame = ttk.Frame(self.notebook)
        self.notebook.add(suppliers_frame, text="الموردين")
        
        # أزرار الموردين
        buttons_frame = ttk.Frame(suppliers_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="إضافة مورد", command=self.add_supplier).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="تعديل مورد", command=self.edit_supplier).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="حذف مورد", command=self.delete_supplier).pack(side=tk.LEFT, padx=5)
        
        # جدول الموردين
        columns = ("المعرف", "الاسم", "الهاتف", "البريد الإلكتروني", "العنوان")
        self.suppliers_tree = ttk.Treeview(suppliers_frame, columns=columns, show="headings")
        
        for col in columns:
            self.suppliers_tree.heading(col, text=col)
            self.suppliers_tree.column(col, width=150)
        
        # شريط التمرير للموردين
        suppliers_scrollbar = ttk.Scrollbar(suppliers_frame, orient=tk.VERTICAL, command=self.suppliers_tree.yview)
        self.suppliers_tree.configure(yscrollcommand=suppliers_scrollbar.set)
        
        self.suppliers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        suppliers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def create_diamonds_tab(self):
        """إنشاء تبويب الألماس"""
        diamonds_frame = ttk.Frame(self.notebook)
        self.notebook.add(diamonds_frame, text="الألماس")
        
        # أزرار الألماس
        buttons_frame = ttk.Frame(diamonds_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="إضافة ألماس", command=self.add_diamond).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="تعديل ألماس", command=self.edit_diamond).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="حذف ألماس", command=self.delete_diamond).pack(side=tk.LEFT, padx=5)
        
        # جدول الألماس
        columns = ("المعرف", "القيراط", "اللون", "النقاء", "القطع", "الشكل", "سعر التكلفة", "سعر البيع")
        self.diamonds_tree = ttk.Treeview(diamonds_frame, columns=columns, show="headings")
        
        for col in columns:
            self.diamonds_tree.heading(col, text=col)
            self.diamonds_tree.column(col, width=100)
        
        # شريط التمرير للألماس
        diamonds_scrollbar = ttk.Scrollbar(diamonds_frame, orient=tk.VERTICAL, command=self.diamonds_tree.yview)
        self.diamonds_tree.configure(yscrollcommand=diamonds_scrollbar.set)
        
        self.diamonds_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        diamonds_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

    def create_sales_tab(self):
        """إنشاء تبويب المبيعات"""
        sales_frame = ttk.Frame(self.notebook)
        self.notebook.add(sales_frame, text="المبيعات")

        # أزرار المبيعات
        buttons_frame = ttk.Frame(sales_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(buttons_frame, text="مبيعات جديدة", command=self.new_sale).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="عرض تفاصيل", command=self.view_sale_details).pack(side=tk.LEFT, padx=5)

        # جدول المبيعات
        columns = ("المعرف", "العميل", "التاريخ", "المبلغ الإجمالي", "العملة", "الحالة")
        self.sales_tree = ttk.Treeview(sales_frame, columns=columns, show="headings")

        for col in columns:
            self.sales_tree.heading(col, text=col)
            self.sales_tree.column(col, width=120)

        # شريط التمرير للمبيعات
        sales_scrollbar = ttk.Scrollbar(sales_frame, orient=tk.VERTICAL, command=self.sales_tree.yview)
        self.sales_tree.configure(yscrollcommand=sales_scrollbar.set)

        self.sales_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        sales_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

    def create_purchases_tab(self):
        """إنشاء تبويب المشتريات"""
        purchases_frame = ttk.Frame(self.notebook)
        self.notebook.add(purchases_frame, text="المشتريات")

        # أزرار المشتريات
        buttons_frame = ttk.Frame(purchases_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(buttons_frame, text="مشتريات جديدة", command=self.new_purchase).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="عرض تفاصيل", command=self.view_purchase_details).pack(side=tk.LEFT, padx=5)

        # جدول المشتريات
        columns = ("المعرف", "المورد", "التاريخ", "المبلغ الإجمالي", "العملة", "الحالة")
        self.purchases_tree = ttk.Treeview(purchases_frame, columns=columns, show="headings")

        for col in columns:
            self.purchases_tree.heading(col, text=col)
            self.purchases_tree.column(col, width=120)

        # شريط التمرير للمشتريات
        purchases_scrollbar = ttk.Scrollbar(purchases_frame, orient=tk.VERTICAL, command=self.purchases_tree.yview)
        self.purchases_tree.configure(yscrollcommand=purchases_scrollbar.set)

        self.purchases_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        purchases_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk.Label(self.root, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def refresh_data(self):
        """تحديث جميع البيانات"""
        try:
            self.update_dashboard()
            self.load_customers()
            self.load_suppliers()
            self.load_diamonds()
            self.load_sales()
            self.load_purchases()
            self.load_inventory()
            self.status_bar.config(text="تم تحديث البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث البيانات: {e}")

    def update_dashboard(self):
        """تحديث لوحة التحكم"""
        try:
            # عدد العملاء
            customers = self.customer_repo.get_active_customers()
            self.customers_count_label.config(text=str(len(customers)))

            # عدد الموردين
            suppliers = self.supplier_repo.get_active_suppliers()
            self.suppliers_count_label.config(text=str(len(suppliers)))

            # عدد قطع الألماس
            diamonds = self.diamond_repo.get_active_diamonds()
            self.diamonds_count_label.config(text=str(len(diamonds)))

            # قيمة المخزون
            inventory_value = self.inventory_manager.get_inventory_value()
            self.inventory_value_label.config(text=f"${inventory_value['total_selling_value']:,.2f}")

            # معلومات سريعة
            self.info_label.config(text=f"العملاء: {len(customers)} | الموردين: {len(suppliers)} | الألماس: {len(diamonds)}")

        except Exception as e:
            print(f"خطأ في تحديث لوحة التحكم: {e}")

    def load_inventory(self):
        """تحميل بيانات المخزون"""
        try:
            # مسح البيانات الحالية
            for item in self.inventory_tree.get_children():
                self.inventory_tree.delete(item)

            # تحميل المخزون المتاح
            inventory = self.inventory_manager.get_available_inventory()

            for item in inventory:
                self.inventory_tree.insert("", "end", values=(
                    item.get('DiamondID', ''),
                    item.get('Carat', ''),
                    item.get('Color', ''),
                    item.get('Clarity', ''),
                    item.get('Quantity', ''),
                    f"${item.get('SellingPrice', 0):,.2f}"
                ))
        except Exception as e:
            print(f"خطأ في تحميل المخزون: {e}")

    def load_customers(self):
        """تحميل بيانات العملاء"""
        try:
            # مسح البيانات الحالية
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            # تحميل العملاء
            customers = self.customer_repo.get_active_customers()

            for customer in customers:
                self.customers_tree.insert("", "end", values=(
                    customer.get('CustomerID', ''),
                    customer.get('Name', ''),
                    customer.get('Phone', ''),
                    customer.get('Email', ''),
                    customer.get('Address', '')
                ))
        except Exception as e:
            print(f"خطأ في تحميل العملاء: {e}")

    def load_suppliers(self):
        """تحميل بيانات الموردين"""
        try:
            # مسح البيانات الحالية
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            # تحميل الموردين
            suppliers = self.supplier_repo.get_active_suppliers()

            for supplier in suppliers:
                self.suppliers_tree.insert("", "end", values=(
                    supplier.get('SupplierID', ''),
                    supplier.get('Name', ''),
                    supplier.get('Phone', ''),
                    supplier.get('Email', ''),
                    supplier.get('Address', '')
                ))
        except Exception as e:
            print(f"خطأ في تحميل الموردين: {e}")

    def load_diamonds(self):
        """تحميل بيانات الألماس"""
        try:
            # مسح البيانات الحالية
            for item in self.diamonds_tree.get_children():
                self.diamonds_tree.delete(item)

            # تحميل الألماس
            diamonds = self.diamond_repo.get_active_diamonds()

            for diamond in diamonds:
                self.diamonds_tree.insert("", "end", values=(
                    diamond.get('DiamondID', ''),
                    diamond.get('Carat', ''),
                    diamond.get('Color', ''),
                    diamond.get('Clarity', ''),
                    diamond.get('Cut', ''),
                    diamond.get('Shape', ''),
                    f"${diamond.get('CostPrice', 0):,.2f}",
                    f"${diamond.get('SellingPrice', 0):,.2f}"
                ))
        except Exception as e:
            print(f"خطأ في تحميل الألماس: {e}")

    def load_sales(self):
        """تحميل بيانات المبيعات"""
        try:
            # مسح البيانات الحالية
            for item in self.sales_tree.get_children():
                self.sales_tree.delete(item)

            # تحميل المبيعات
            sales = self.sales_manager.get_sales_by_date_range(
                date.today().replace(day=1), date.today()
            )

            for sale in sales:
                # الحصول على اسم العميل
                customer_name = "غير محدد"
                if sale.get('CustomerID'):
                    customer = self.customer_repo.get_by_id(sale['CustomerID'])
                    if customer:
                        customer_name = customer.get('Name', 'غير محدد')

                self.sales_tree.insert("", "end", values=(
                    sale.get('SaleID', ''),
                    customer_name,
                    sale.get('SaleDate', ''),
                    f"${sale.get('TotalAmount', 0):,.2f}",
                    "USD",  # يمكن تحسينها لاحقاً
                    sale.get('PaymentStatus', '')
                ))
        except Exception as e:
            print(f"خطأ في تحميل المبيعات: {e}")

    def load_purchases(self):
        """تحميل بيانات المشتريات"""
        try:
            # مسح البيانات الحالية
            for item in self.purchases_tree.get_children():
                self.purchases_tree.delete(item)

            # تحميل المشتريات
            purchases = self.purchase_manager.get_purchases_by_date_range(
                date.today().replace(day=1), date.today()
            )

            for purchase in purchases:
                # الحصول على اسم المورد
                supplier_name = "غير محدد"
                if purchase.get('SupplierID'):
                    supplier = self.supplier_repo.get_by_id(purchase['SupplierID'])
                    if supplier:
                        supplier_name = supplier.get('Name', 'غير محدد')

                self.purchases_tree.insert("", "end", values=(
                    purchase.get('PurchaseID', ''),
                    supplier_name,
                    purchase.get('PurchaseDate', ''),
                    f"${purchase.get('TotalAmount', 0):,.2f}",
                    "USD",  # يمكن تحسينها لاحقاً
                    purchase.get('PaymentStatus', '')
                ))
        except Exception as e:
            print(f"خطأ في تحميل المشتريات: {e}")

    # دوال العمليات
    def add_customer(self):
        """إضافة عميل جديد"""
        dialog = CustomerDialog(self.root, "إضافة عميل جديد")
        if dialog.result:
            try:
                customer_data = {
                    'Name': dialog.result['name'],
                    'Phone': dialog.result['phone'],
                    'Email': dialog.result['email'],
                    'Address': dialog.result['address'],
                    'ContactInfo': dialog.result.get('contact_info', ''),
                    'IsActive': True,
                    'CreatedAt': datetime.now()
                }

                customer_id = self.customer_repo.insert(customer_data)
                messagebox.showinfo("نجح", f"تم إضافة العميل بنجاح! المعرف: {customer_id}")
                self.load_customers()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إضافة العميل: {e}")

    def add_supplier(self):
        """إضافة مورد جديد"""
        dialog = SupplierDialog(self.root, "إضافة مورد جديد")
        if dialog.result:
            try:
                supplier_data = {
                    'Name': dialog.result['name'],
                    'Phone': dialog.result['phone'],
                    'Email': dialog.result['email'],
                    'Address': dialog.result['address'],
                    'ContactInfo': dialog.result.get('contact_info', ''),
                    'IsActive': True,
                    'CreatedAt': datetime.now()
                }

                supplier_id = self.supplier_repo.insert(supplier_data)
                messagebox.showinfo("نجح", f"تم إضافة المورد بنجاح! المعرف: {supplier_id}")
                self.load_suppliers()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إضافة المورد: {e}")

    def add_diamond(self):
        """إضافة ألماس جديد"""
        dialog = DiamondDialog(self.root, "إضافة ألماس جديد")
        if dialog.result:
            try:
                # الحصول على العملة الافتراضية
                base_currency = self.currency_manager.get_base_currency()
                currency_id = base_currency['CurrencyID'] if base_currency else 1

                diamond_id = self.inventory_manager.add_diamond(
                    carat=Decimal(str(dialog.result['carat'])),
                    color=dialog.result['color'],
                    clarity=dialog.result['clarity'],
                    cut=dialog.result['cut'],
                    shape=dialog.result['shape'],
                    certificate_number=dialog.result['certificate'],
                    cost_price=Decimal(str(dialog.result['cost_price'])),
                    selling_price=Decimal(str(dialog.result['selling_price'])),
                    currency_id=currency_id,
                    description=dialog.result.get('description', '')
                )

                messagebox.showinfo("نجح", f"تم إضافة الألماس بنجاح! المعرف: {diamond_id}")
                self.load_diamonds()
                self.load_inventory()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إضافة الألماس: {e}")

    def new_sale(self):
        """إنشاء مبيعات جديدة"""
        dialog = SaleDialog(self.root, self.customer_repo, self.diamond_repo, self.inventory_manager)
        if dialog.result:
            try:
                # إنشاء المبيعات
                sale_id = self.sales_manager.create_sale(
                    customer_id=dialog.result.get('customer_id'),
                    sale_date=date.today(),
                    currency_id=1,  # USD افتراضي
                    notes=dialog.result.get('notes', '')
                )

                # إضافة العناصر
                for item in dialog.result['items']:
                    self.sales_manager.add_sale_item(
                        sale_id=sale_id,
                        diamond_id=item['diamond_id'],
                        quantity=item['quantity'],
                        unit_price=Decimal(str(item['unit_price']))
                    )

                # إنهاء المبيعات
                self.sales_manager.finalize_sale(sale_id)

                messagebox.showinfo("نجح", f"تم إنشاء المبيعات بنجاح! رقم الفاتورة: {sale_id}")
                self.load_sales()
                self.load_inventory()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إنشاء المبيعات: {e}")

    def new_purchase(self):
        """إنشاء مشتريات جديدة"""
        dialog = PurchaseDialog(self.root, self.supplier_repo, self.diamond_repo)
        if dialog.result:
            try:
                # إنشاء المشتريات
                purchase_id = self.purchase_manager.create_purchase(
                    supplier_id=dialog.result.get('supplier_id'),
                    purchase_date=date.today(),
                    currency_id=1,  # USD افتراضي
                    notes=dialog.result.get('notes', '')
                )

                # إضافة العناصر
                for item in dialog.result['items']:
                    self.purchase_manager.add_purchase_item(
                        purchase_id=purchase_id,
                        diamond_id=item['diamond_id'],
                        quantity=item['quantity'],
                        unit_price=Decimal(str(item['unit_price']))
                    )

                # إنهاء المشتريات
                self.purchase_manager.finalize_purchase(purchase_id)

                messagebox.showinfo("نجح", f"تم إنشاء المشتريات بنجاح! رقم الفاتورة: {purchase_id}")
                self.load_purchases()
                self.load_inventory()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إنشاء المشتريات: {e}")

    # دوال أخرى
    def edit_customer(self):
        """تعديل عميل"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return

        item = self.customers_tree.item(selection[0])
        customer_id = item['values'][0]

        # الحصول على بيانات العميل
        customer = self.customer_repo.get_by_id(customer_id)
        if customer:
            dialog = CustomerDialog(self.root, "تعديل عميل", customer)
            if dialog.result:
                try:
                    update_data = {
                        'Name': dialog.result['name'],
                        'Phone': dialog.result['phone'],
                        'Email': dialog.result['email'],
                        'Address': dialog.result['address']
                    }

                    self.customer_repo.update(customer_id, update_data)
                    messagebox.showinfo("نجح", "تم تحديث بيانات العميل بنجاح!")
                    self.load_customers()
                except Exception as e:
                    messagebox.showerror("خطأ", f"خطأ في تحديث العميل: {e}")

    def delete_customer(self):
        """حذف عميل"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return

        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا العميل؟"):
            item = self.customers_tree.item(selection[0])
            customer_id = item['values'][0]

            try:
                self.customer_repo.update(customer_id, {'IsActive': False})
                messagebox.showinfo("نجح", "تم حذف العميل بنجاح!")
                self.load_customers()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف العميل: {e}")

    def edit_supplier(self):
        """تعديل مورد"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def delete_supplier(self):
        """حذف مورد"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def edit_diamond(self):
        """تعديل ألماس"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def delete_diamond(self):
        """حذف ألماس"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def view_sale_details(self):
        """عرض تفاصيل المبيعات"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def view_purchase_details(self):
        """عرض تفاصيل المشتريات"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def new_receipt(self):
        """سند قبض جديد"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def new_payment(self):
        """سند صرف جديد"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def inventory_report(self):
        """تقرير المخزون"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def sales_report(self):
        """تقرير المبيعات"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def purchases_report(self):
        """تقرير المشتريات"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def new_database(self):
        """قاعدة بيانات جديدة"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def open_database(self):
        """فتح قاعدة بيانات"""
        messagebox.showinfo("قريباً", "هذه الميزة قيد التطوير")

    def show_customers(self):
        """عرض العملاء"""
        self.notebook.select(1)  # تبويب العملاء

    def show_suppliers(self):
        """عرض الموردين"""
        self.notebook.select(2)  # تبويب الموردين

    def show_diamonds(self):
        """عرض الألماس"""
        self.notebook.select(3)  # تبويب الألماس

    def show_about(self):
        """حول البرنامج"""
        about_text = """
نظام إدارة مبيعات الألماس
الإصدار 1.0

نظام شامل لإدارة مبيعات الألماس يدعم:
• إدارة العملاء والموردين
• إدارة المخزون والألماس
• عمليات البيع والشراء
• النظام المحاسبي
• دعم العملات المتعددة

تم التطوير بلغة Python
        """
        messagebox.showinfo("حول البرنامج", about_text)

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

    def __del__(self):
        """تنظيف الموارد"""
        if hasattr(self, 'db_schema'):
            self.db_schema.close()

"""
مخطط قاعدة البيانات لنظام إدارة مبيعات الألماس
"""

import sqlite3
from typing import Optional


class DatabaseSchema:
    """فئة لإنشاء وإدارة مخطط قاعدة البيانات"""
    
    def __init__(self, db_path: str = "diamonds.db"):
        """
        تهيئة قاعدة البيانات
        
        Args:
            db_path: مسار ملف قاعدة البيانات
        """
        self.db_path = db_path
        self.connection: Optional[sqlite3.Connection] = None
    
    def connect(self) -> sqlite3.Connection:
        """الاتصال بقاعدة البيانات"""
        if not self.connection:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return self.connection
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def create_tables(self):
        """إنشاء جميع الجداول في قاعدة البيانات"""
        conn = self.connect()
        cursor = conn.cursor()
        
        # جدول العملات
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Currencies (
            CurrencyID INTEGER PRIMARY KEY AUTOINCREMENT,
            CurrencyCode TEXT NOT NULL UNIQUE,
            CurrencyName TEXT NOT NULL,
            Symbol TEXT,
            IsBaseCurrency BOOLEAN DEFAULT FALSE,
            CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """)
        
        # جدول أسعار الصرف
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS ExchangeRates (
            RateID INTEGER PRIMARY KEY AUTOINCREMENT,
            FromCurrencyID INTEGER NOT NULL,
            ToCurrencyID INTEGER NOT NULL,
            ExchangeRate REAL NOT NULL,
            Date DATE NOT NULL,
            CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (FromCurrencyID) REFERENCES Currencies(CurrencyID),
            FOREIGN KEY (ToCurrencyID) REFERENCES Currencies(CurrencyID),
            UNIQUE(FromCurrencyID, ToCurrencyID, Date)
        );
        """)
        
        # جدول العملاء
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Customers (
            CustomerID INTEGER PRIMARY KEY AUTOINCREMENT,
            Name TEXT NOT NULL,
            ContactInfo TEXT,
            Address TEXT,
            Email TEXT,
            Phone TEXT,
            IsActive BOOLEAN DEFAULT TRUE,
            CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """)
        
        # جدول الموردين
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Suppliers (
            SupplierID INTEGER PRIMARY KEY AUTOINCREMENT,
            Name TEXT NOT NULL,
            ContactInfo TEXT,
            Address TEXT,
            Email TEXT,
            Phone TEXT,
            IsActive BOOLEAN DEFAULT TRUE,
            CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """)
        
        # جدول الألماس
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Diamonds (
            DiamondID INTEGER PRIMARY KEY AUTOINCREMENT,
            Carat REAL NOT NULL,
            Color TEXT,
            Clarity TEXT,
            Cut TEXT,
            Shape TEXT,
            CertificateNumber TEXT,
            CostPrice REAL,
            SellingPrice REAL,
            CurrencyID INTEGER,
            Description TEXT,
            IsActive BOOLEAN DEFAULT TRUE,
            CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (CurrencyID) REFERENCES Currencies(CurrencyID)
        );
        """)
        
        # جدول المخزون
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Inventory (
            InventoryID INTEGER PRIMARY KEY AUTOINCREMENT,
            DiamondID INTEGER NOT NULL,
            Quantity INTEGER NOT NULL DEFAULT 0,
            ReservedQuantity INTEGER DEFAULT 0,
            AvailableQuantity INTEGER GENERATED ALWAYS AS (Quantity - ReservedQuantity) STORED,
            LastUpdated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (DiamondID) REFERENCES Diamonds(DiamondID),
            UNIQUE(DiamondID)
        );
        """)
        
        # جدول المبيعات
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Sales (
            SaleID INTEGER PRIMARY KEY AUTOINCREMENT,
            CustomerID INTEGER,
            SaleDate DATE NOT NULL,
            TotalAmount REAL NOT NULL,
            CurrencyID INTEGER NOT NULL,
            PaymentStatus TEXT DEFAULT 'PENDING',
            Notes TEXT,
            CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
            FOREIGN KEY (CurrencyID) REFERENCES Currencies(CurrencyID)
        );
        """)
        
        # جدول تفاصيل المبيعات
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS SaleItems (
            SaleItemID INTEGER PRIMARY KEY AUTOINCREMENT,
            SaleID INTEGER NOT NULL,
            DiamondID INTEGER NOT NULL,
            Quantity INTEGER NOT NULL,
            UnitPrice REAL NOT NULL,
            TotalPrice REAL NOT NULL,
            FOREIGN KEY (SaleID) REFERENCES Sales(SaleID) ON DELETE CASCADE,
            FOREIGN KEY (DiamondID) REFERENCES Diamonds(DiamondID)
        );
        """)
        
        # جدول المشتريات
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Purchases (
            PurchaseID INTEGER PRIMARY KEY AUTOINCREMENT,
            SupplierID INTEGER,
            PurchaseDate DATE NOT NULL,
            TotalAmount REAL NOT NULL,
            CurrencyID INTEGER NOT NULL,
            PaymentStatus TEXT DEFAULT 'PENDING',
            Notes TEXT,
            CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
            FOREIGN KEY (CurrencyID) REFERENCES Currencies(CurrencyID)
        );
        """)
        
        # جدول تفاصيل المشتريات
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS PurchaseItems (
            PurchaseItemID INTEGER PRIMARY KEY AUTOINCREMENT,
            PurchaseID INTEGER NOT NULL,
            DiamondID INTEGER NOT NULL,
            Quantity INTEGER NOT NULL,
            UnitPrice REAL NOT NULL,
            TotalPrice REAL NOT NULL,
            FOREIGN KEY (PurchaseID) REFERENCES Purchases(PurchaseID) ON DELETE CASCADE,
            FOREIGN KEY (DiamondID) REFERENCES Diamonds(DiamondID)
        );
        """)
        
        # جدول سندات القبض
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Receipts (
            ReceiptID INTEGER PRIMARY KEY AUTOINCREMENT,
            CustomerID INTEGER,
            Amount REAL NOT NULL,
            ReceiptDate DATE NOT NULL,
            CurrencyID INTEGER NOT NULL,
            PaymentMethod TEXT,
            ReferenceNumber TEXT,
            Notes TEXT,
            CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
            FOREIGN KEY (CurrencyID) REFERENCES Currencies(CurrencyID)
        );
        """)

        # جدول سندات الصرف
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Payments (
            PaymentID INTEGER PRIMARY KEY AUTOINCREMENT,
            SupplierID INTEGER,
            Amount REAL NOT NULL,
            PaymentDate DATE NOT NULL,
            CurrencyID INTEGER NOT NULL,
            PaymentMethod TEXT,
            ReferenceNumber TEXT,
            Notes TEXT,
            CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
            FOREIGN KEY (CurrencyID) REFERENCES Currencies(CurrencyID)
        );
        """)

        # جدول النقدية
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS Cash (
            CashID INTEGER PRIMARY KEY AUTOINCREMENT,
            Amount REAL NOT NULL,
            CurrencyID INTEGER NOT NULL,
            TransactionDate DATE NOT NULL,
            TransactionType TEXT NOT NULL,
            Description TEXT,
            ReferenceID INTEGER,
            ReferenceType TEXT,
            CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (CurrencyID) REFERENCES Currencies(CurrencyID)
        );
        """)

        # جدول الدليل المحاسبي
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS ChartOfAccounts (
            AccountID INTEGER PRIMARY KEY AUTOINCREMENT,
            AccountCode TEXT NOT NULL UNIQUE,
            AccountName TEXT NOT NULL,
            AccountType TEXT NOT NULL,
            ParentAccountID INTEGER,
            IsActive BOOLEAN DEFAULT TRUE,
            Description TEXT,
            CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (ParentAccountID) REFERENCES ChartOfAccounts(AccountID)
        );
        """)

        # جدول القيود اليومية
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS JournalEntries (
            EntryID INTEGER PRIMARY KEY AUTOINCREMENT,
            EntryDate DATE NOT NULL,
            Description TEXT,
            ReferenceNumber TEXT,
            TotalDebit REAL NOT NULL,
            TotalCredit REAL NOT NULL,
            IsPosted BOOLEAN DEFAULT FALSE,
            CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """)

        # جدول تفاصيل القيود اليومية
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS JournalEntryDetails (
            DetailID INTEGER PRIMARY KEY AUTOINCREMENT,
            EntryID INTEGER NOT NULL,
            AccountID INTEGER NOT NULL,
            DebitAmount REAL DEFAULT 0,
            CreditAmount REAL DEFAULT 0,
            Description TEXT,
            FOREIGN KEY (EntryID) REFERENCES JournalEntries(EntryID) ON DELETE CASCADE,
            FOREIGN KEY (AccountID) REFERENCES ChartOfAccounts(AccountID)
        );
        """)

        # إنشاء الفهارس لتحسين الأداء
        self._create_indexes(cursor)

        conn.commit()
        return True

    def _create_indexes(self, cursor):
        """إنشاء الفهارس لتحسين أداء الاستعلامات"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_sales_date ON Sales(SaleDate);",
            "CREATE INDEX IF NOT EXISTS idx_purchases_date ON Purchases(PurchaseDate);",
            "CREATE INDEX IF NOT EXISTS idx_receipts_date ON Receipts(ReceiptDate);",
            "CREATE INDEX IF NOT EXISTS idx_payments_date ON Payments(PaymentDate);",
            "CREATE INDEX IF NOT EXISTS idx_cash_date ON Cash(TransactionDate);",
            "CREATE INDEX IF NOT EXISTS idx_journal_date ON JournalEntries(EntryDate);",
            "CREATE INDEX IF NOT EXISTS idx_exchange_rates_date ON ExchangeRates(Date);",
            "CREATE INDEX IF NOT EXISTS idx_diamonds_active ON Diamonds(IsActive);",
            "CREATE INDEX IF NOT EXISTS idx_customers_active ON Customers(IsActive);",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_active ON Suppliers(IsActive);"
        ]

        for index_sql in indexes:
            cursor.execute(index_sql)

    def insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        conn = self.connect()
        cursor = conn.cursor()

        # إدراج العملات الافتراضية
        default_currencies = [
            ('USD', 'US Dollar', '$', False),
            ('SAR', 'Saudi Riyal', 'ر.س', True),
            ('EUR', 'Euro', '€', False)
        ]

        cursor.executemany("""
        INSERT OR IGNORE INTO Currencies (CurrencyCode, CurrencyName, Symbol, IsBaseCurrency)
        VALUES (?, ?, ?, ?)
        """, default_currencies)

        # إدراج الحسابات المحاسبية الافتراضية
        default_accounts = [
            ('1000', 'الأصول', 'ASSET', None),
            ('1100', 'الأصول المتداولة', 'ASSET', 1),
            ('1110', 'النقدية', 'ASSET', 2),
            ('1120', 'المخزون', 'ASSET', 2),
            ('2000', 'الخصوم', 'LIABILITY', None),
            ('2100', 'الخصوم المتداولة', 'LIABILITY', 5),
            ('2110', 'حسابات دائنة', 'LIABILITY', 6),
            ('3000', 'حقوق الملكية', 'EQUITY', None),
            ('3100', 'رأس المال', 'EQUITY', 8),
            ('4000', 'الإيرادات', 'REVENUE', None),
            ('4100', 'مبيعات الألماس', 'REVENUE', 10),
            ('5000', 'المصروفات', 'EXPENSE', None),
            ('5100', 'تكلفة البضاعة المباعة', 'EXPENSE', 12)
        ]

        for code, name, acc_type, parent_id in default_accounts:
            cursor.execute("""
            INSERT OR IGNORE INTO ChartOfAccounts (AccountCode, AccountName, AccountType, ParentAccountID)
            VALUES (?, ?, ?, ?)
            """, (code, name, acc_type, parent_id))

        conn.commit()
        return True

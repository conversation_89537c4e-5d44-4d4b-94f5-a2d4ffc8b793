"""
نوافذ الحوار للتطبيق
"""

import tkinter as tk
from tkinter import ttk, messagebox
from decimal import Decimal
from typing import Optional, Dict, Any, List


class BaseDialog:
    """نافذة حوار أساسية"""
    
    def __init__(self, parent, title, data=None):
        self.result = None
        self.data = data or {}
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء المحتوى
        self.create_widgets()
        
        # التركيز على النافذة
        self.dialog.focus_set()
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر النافذة - يجب تنفيذها في الفئات المشتقة"""
        pass
    
    def ok_clicked(self):
        """عند النقر على موافق"""
        if self.validate():
            self.result = self.get_data()
            self.dialog.destroy()
    
    def cancel_clicked(self):
        """عند النقر على إلغاء"""
        self.dialog.destroy()
    
    def validate(self):
        """التحقق من صحة البيانات"""
        return True
    
    def get_data(self):
        """الحصول على البيانات"""
        return {}


class CustomerDialog(BaseDialog):
    """نافذة حوار العميل"""
    
    def create_widgets(self):
        """إنشاء عناصر نافذة العميل"""
        # إطار المحتوى
        content_frame = ttk.Frame(self.dialog, padding="10")
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # الاسم
        ttk.Label(content_frame, text="اسم العميل:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar(value=self.data.get('Name', ''))
        ttk.Entry(content_frame, textvariable=self.name_var, width=30).grid(row=0, column=1, pady=5, padx=(10, 0))
        
        # الهاتف
        ttk.Label(content_frame, text="رقم الهاتف:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.phone_var = tk.StringVar(value=self.data.get('Phone', ''))
        ttk.Entry(content_frame, textvariable=self.phone_var, width=30).grid(row=1, column=1, pady=5, padx=(10, 0))
        
        # البريد الإلكتروني
        ttk.Label(content_frame, text="البريد الإلكتروني:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.email_var = tk.StringVar(value=self.data.get('Email', ''))
        ttk.Entry(content_frame, textvariable=self.email_var, width=30).grid(row=2, column=1, pady=5, padx=(10, 0))
        
        # العنوان
        ttk.Label(content_frame, text="العنوان:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.address_var = tk.StringVar(value=self.data.get('Address', ''))
        ttk.Entry(content_frame, textvariable=self.address_var, width=30).grid(row=3, column=1, pady=5, padx=(10, 0))
        
        # الأزرار
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        ttk.Button(buttons_frame, text="موافق", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)
    
    def validate(self):
        """التحقق من صحة بيانات العميل"""
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "اسم العميل مطلوب!")
            return False
        return True
    
    def get_data(self):
        """الحصول على بيانات العميل"""
        return {
            'name': self.name_var.get().strip(),
            'phone': self.phone_var.get().strip(),
            'email': self.email_var.get().strip(),
            'address': self.address_var.get().strip()
        }


class SupplierDialog(BaseDialog):
    """نافذة حوار المورد"""
    
    def create_widgets(self):
        """إنشاء عناصر نافذة المورد"""
        # إطار المحتوى
        content_frame = ttk.Frame(self.dialog, padding="10")
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # الاسم
        ttk.Label(content_frame, text="اسم المورد:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar(value=self.data.get('Name', ''))
        ttk.Entry(content_frame, textvariable=self.name_var, width=30).grid(row=0, column=1, pady=5, padx=(10, 0))
        
        # الهاتف
        ttk.Label(content_frame, text="رقم الهاتف:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.phone_var = tk.StringVar(value=self.data.get('Phone', ''))
        ttk.Entry(content_frame, textvariable=self.phone_var, width=30).grid(row=1, column=1, pady=5, padx=(10, 0))
        
        # البريد الإلكتروني
        ttk.Label(content_frame, text="البريد الإلكتروني:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.email_var = tk.StringVar(value=self.data.get('Email', ''))
        ttk.Entry(content_frame, textvariable=self.email_var, width=30).grid(row=2, column=1, pady=5, padx=(10, 0))
        
        # العنوان
        ttk.Label(content_frame, text="العنوان:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.address_var = tk.StringVar(value=self.data.get('Address', ''))
        ttk.Entry(content_frame, textvariable=self.address_var, width=30).grid(row=3, column=1, pady=5, padx=(10, 0))
        
        # الأزرار
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        ttk.Button(buttons_frame, text="موافق", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)
    
    def validate(self):
        """التحقق من صحة بيانات المورد"""
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "اسم المورد مطلوب!")
            return False
        return True
    
    def get_data(self):
        """الحصول على بيانات المورد"""
        return {
            'name': self.name_var.get().strip(),
            'phone': self.phone_var.get().strip(),
            'email': self.email_var.get().strip(),
            'address': self.address_var.get().strip()
        }


class DiamondDialog(BaseDialog):
    """نافذة حوار الألماس"""
    
    def __init__(self, parent, title, data=None):
        self.dialog = None
        super().__init__(parent, title, data)
    
    def create_widgets(self):
        """إنشاء عناصر نافذة الألماس"""
        self.dialog.geometry("500x400")
        
        # إطار المحتوى
        content_frame = ttk.Frame(self.dialog, padding="10")
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # القيراط
        ttk.Label(content_frame, text="القيراط:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.carat_var = tk.StringVar(value=str(self.data.get('Carat', '')))
        ttk.Entry(content_frame, textvariable=self.carat_var, width=20).grid(row=0, column=1, pady=5, padx=(10, 0))
        
        # اللون
        ttk.Label(content_frame, text="اللون:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.color_var = tk.StringVar(value=self.data.get('Color', ''))
        color_combo = ttk.Combobox(content_frame, textvariable=self.color_var, width=18)
        color_combo['values'] = ('D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M')
        color_combo.grid(row=1, column=1, pady=5, padx=(10, 0))
        
        # النقاء
        ttk.Label(content_frame, text="النقاء:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.clarity_var = tk.StringVar(value=self.data.get('Clarity', ''))
        clarity_combo = ttk.Combobox(content_frame, textvariable=self.clarity_var, width=18)
        clarity_combo['values'] = ('FL', 'IF', 'VVS1', 'VVS2', 'VS1', 'VS2', 'SI1', 'SI2', 'I1', 'I2', 'I3')
        clarity_combo.grid(row=2, column=1, pady=5, padx=(10, 0))
        
        # القطع
        ttk.Label(content_frame, text="القطع:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.cut_var = tk.StringVar(value=self.data.get('Cut', ''))
        cut_combo = ttk.Combobox(content_frame, textvariable=self.cut_var, width=18)
        cut_combo['values'] = ('Excellent', 'Very Good', 'Good', 'Fair', 'Poor')
        cut_combo.grid(row=3, column=1, pady=5, padx=(10, 0))
        
        # الشكل
        ttk.Label(content_frame, text="الشكل:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.shape_var = tk.StringVar(value=self.data.get('Shape', ''))
        shape_combo = ttk.Combobox(content_frame, textvariable=self.shape_var, width=18)
        shape_combo['values'] = ('Round', 'Princess', 'Oval', 'Emerald', 'Pear', 'Marquise', 'Heart', 'Cushion')
        shape_combo.grid(row=4, column=1, pady=5, padx=(10, 0))
        
        # رقم الشهادة
        ttk.Label(content_frame, text="رقم الشهادة:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.certificate_var = tk.StringVar(value=self.data.get('CertificateNumber', ''))
        ttk.Entry(content_frame, textvariable=self.certificate_var, width=20).grid(row=5, column=1, pady=5, padx=(10, 0))
        
        # سعر التكلفة
        ttk.Label(content_frame, text="سعر التكلفة ($):").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.cost_price_var = tk.StringVar(value=str(self.data.get('CostPrice', '')))
        ttk.Entry(content_frame, textvariable=self.cost_price_var, width=20).grid(row=6, column=1, pady=5, padx=(10, 0))
        
        # سعر البيع
        ttk.Label(content_frame, text="سعر البيع ($):").grid(row=7, column=0, sticky=tk.W, pady=5)
        self.selling_price_var = tk.StringVar(value=str(self.data.get('SellingPrice', '')))
        ttk.Entry(content_frame, textvariable=self.selling_price_var, width=20).grid(row=7, column=1, pady=5, padx=(10, 0))
        
        # الوصف
        ttk.Label(content_frame, text="الوصف:").grid(row=8, column=0, sticky=tk.W, pady=5)
        self.description_var = tk.StringVar(value=self.data.get('Description', ''))
        ttk.Entry(content_frame, textvariable=self.description_var, width=20).grid(row=8, column=1, pady=5, padx=(10, 0))
        
        # الأزرار
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.grid(row=9, column=0, columnspan=2, pady=20)
        
        ttk.Button(buttons_frame, text="موافق", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)
    
    def validate(self):
        """التحقق من صحة بيانات الألماس"""
        try:
            if not self.carat_var.get().strip():
                messagebox.showerror("خطأ", "القيراط مطلوب!")
                return False
            
            float(self.carat_var.get())
            
            if self.cost_price_var.get().strip():
                float(self.cost_price_var.get())
            
            if self.selling_price_var.get().strip():
                float(self.selling_price_var.get())
            
            return True
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للقيراط والأسعار!")
            return False
    
    def get_data(self):
        """الحصول على بيانات الألماس"""
        return {
            'carat': float(self.carat_var.get()),
            'color': self.color_var.get(),
            'clarity': self.clarity_var.get(),
            'cut': self.cut_var.get(),
            'shape': self.shape_var.get(),
            'certificate': self.certificate_var.get(),
            'cost_price': float(self.cost_price_var.get()) if self.cost_price_var.get() else 0,
            'selling_price': float(self.selling_price_var.get()) if self.selling_price_var.get() else 0,
            'description': self.description_var.get()
        }


class SaleDialog(BaseDialog):
    """نافذة حوار المبيعات"""

    def __init__(self, parent, customer_repo, diamond_repo, inventory_manager):
        self.customer_repo = customer_repo
        self.diamond_repo = diamond_repo
        self.inventory_manager = inventory_manager
        self.items = []
        super().__init__(parent, "مبيعات جديدة")

    def create_widgets(self):
        """إنشاء عناصر نافذة المبيعات"""
        self.dialog.geometry("700x500")

        # إطار المحتوى
        content_frame = ttk.Frame(self.dialog, padding="10")
        content_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات المبيعات
        info_frame = ttk.LabelFrame(content_frame, text="معلومات المبيعات")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # العميل
        ttk.Label(info_frame, text="العميل:").grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
        self.customer_var = tk.StringVar()
        customer_combo = ttk.Combobox(info_frame, textvariable=self.customer_var, width=30)

        # تحميل العملاء
        customers = self.customer_repo.get_active_customers()
        customer_values = [f"{c['CustomerID']} - {c['Name']}" for c in customers]
        customer_combo['values'] = customer_values
        customer_combo.grid(row=0, column=1, pady=5, padx=5)

        # الملاحظات
        ttk.Label(info_frame, text="ملاحظات:").grid(row=1, column=0, sticky=tk.W, pady=5, padx=5)
        self.notes_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.notes_var, width=30).grid(row=1, column=1, pady=5, padx=5)

        # عناصر المبيعات
        items_frame = ttk.LabelFrame(content_frame, text="عناصر المبيعات")
        items_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # أزرار إضافة وحذف
        buttons_frame = ttk.Frame(items_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(buttons_frame, text="إضافة عنصر", command=self.add_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="حذف عنصر", command=self.remove_item).pack(side=tk.LEFT, padx=5)

        # جدول العناصر
        columns = ("الألماس", "الكمية", "سعر الوحدة", "الإجمالي")
        self.items_tree = ttk.Treeview(items_frame, columns=columns, show="headings", height=8)

        for col in columns:
            self.items_tree.heading(col, text=col)
            self.items_tree.column(col, width=120)

        self.items_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # الإجمالي
        total_frame = ttk.Frame(content_frame)
        total_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(total_frame, text="الإجمالي:", font=("Arial", 12, "bold")).pack(side=tk.RIGHT, padx=5)
        self.total_label = ttk.Label(total_frame, text="$0.00", font=("Arial", 12, "bold"))
        self.total_label.pack(side=tk.RIGHT, padx=5)

        # الأزرار
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.pack(fill=tk.X)

        ttk.Button(buttons_frame, text="حفظ", command=self.ok_clicked).pack(side=tk.RIGHT, padx=5)
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel_clicked).pack(side=tk.RIGHT, padx=5)

    def add_item(self):
        """إضافة عنصر للمبيعات"""
        dialog = SaleItemDialog(self.dialog, self.diamond_repo, self.inventory_manager)
        if dialog.result:
            self.items.append(dialog.result)
            self.refresh_items()

    def remove_item(self):
        """حذف عنصر من المبيعات"""
        selection = self.items_tree.selection()
        if selection:
            item_index = self.items_tree.index(selection[0])
            del self.items[item_index]
            self.refresh_items()

    def refresh_items(self):
        """تحديث جدول العناصر"""
        # مسح الجدول
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة العناصر
        total = 0
        for item in self.items:
            diamond = self.diamond_repo.get_by_id(item['diamond_id'])
            diamond_name = f"{diamond['Carat']}ct {diamond['Color']} {diamond['Clarity']}" if diamond else "غير معروف"
            item_total = item['quantity'] * item['unit_price']
            total += item_total

            self.items_tree.insert("", "end", values=(
                diamond_name,
                item['quantity'],
                f"${item['unit_price']:,.2f}",
                f"${item_total:,.2f}"
            ))

        # تحديث الإجمالي
        self.total_label.config(text=f"${total:,.2f}")

    def validate(self):
        """التحقق من صحة بيانات المبيعات"""
        if not self.items:
            messagebox.showerror("خطأ", "يجب إضافة عنصر واحد على الأقل!")
            return False
        return True

    def get_data(self):
        """الحصول على بيانات المبيعات"""
        customer_id = None
        if self.customer_var.get():
            customer_id = int(self.customer_var.get().split(' - ')[0])

        return {
            'customer_id': customer_id,
            'notes': self.notes_var.get(),
            'items': self.items
        }


class SaleItemDialog(BaseDialog):
    """نافذة حوار عنصر المبيعات"""

    def __init__(self, parent, diamond_repo, inventory_manager):
        self.diamond_repo = diamond_repo
        self.inventory_manager = inventory_manager
        super().__init__(parent, "إضافة عنصر")

    def create_widgets(self):
        """إنشاء عناصر نافذة عنصر المبيعات"""
        self.dialog.geometry("400x250")

        # إطار المحتوى
        content_frame = ttk.Frame(self.dialog, padding="10")
        content_frame.pack(fill=tk.BOTH, expand=True)

        # الألماس
        ttk.Label(content_frame, text="الألماس:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.diamond_var = tk.StringVar()
        diamond_combo = ttk.Combobox(content_frame, textvariable=self.diamond_var, width=30)

        # تحميل الألماس المتاح
        available_inventory = self.inventory_manager.get_available_inventory()
        diamond_values = [f"{item['DiamondID']} - {item['Carat']}ct {item['Color']} {item['Clarity']}"
                         for item in available_inventory if item['Quantity'] > 0]
        diamond_combo['values'] = diamond_values
        diamond_combo.grid(row=0, column=1, pady=5, padx=(10, 0))

        # الكمية
        ttk.Label(content_frame, text="الكمية:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.quantity_var = tk.StringVar(value="1")
        ttk.Entry(content_frame, textvariable=self.quantity_var, width=30).grid(row=1, column=1, pady=5, padx=(10, 0))

        # سعر الوحدة
        ttk.Label(content_frame, text="سعر الوحدة ($):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.unit_price_var = tk.StringVar()
        ttk.Entry(content_frame, textvariable=self.unit_price_var, width=30).grid(row=2, column=1, pady=5, padx=(10, 0))

        # ربط تغيير الألماس بتحديث السعر
        diamond_combo.bind('<<ComboboxSelected>>', self.on_diamond_selected)

        # الأزرار
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=20)

        ttk.Button(buttons_frame, text="موافق", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)

    def on_diamond_selected(self, event):
        """عند اختيار ألماس"""
        if self.diamond_var.get():
            diamond_id = int(self.diamond_var.get().split(' - ')[0])
            diamond = self.diamond_repo.get_by_id(diamond_id)
            if diamond:
                self.unit_price_var.set(str(diamond.get('SellingPrice', 0)))

    def validate(self):
        """التحقق من صحة بيانات العنصر"""
        try:
            if not self.diamond_var.get():
                messagebox.showerror("خطأ", "يرجى اختيار ألماس!")
                return False

            quantity = int(self.quantity_var.get())
            if quantity <= 0:
                messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر!")
                return False

            unit_price = float(self.unit_price_var.get())
            if unit_price <= 0:
                messagebox.showerror("خطأ", "سعر الوحدة يجب أن يكون أكبر من صفر!")
                return False

            # التحقق من توفر الكمية
            diamond_id = int(self.diamond_var.get().split(' - ')[0])
            inventory = self.inventory_manager.inventory_repo.get_by_diamond_id(diamond_id)
            if not inventory or inventory['AvailableQuantity'] < quantity:
                messagebox.showerror("خطأ", "الكمية المطلوبة غير متوفرة في المخزون!")
                return False

            return True
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة!")
            return False

    def get_data(self):
        """الحصول على بيانات العنصر"""
        diamond_id = int(self.diamond_var.get().split(' - ')[0])
        return {
            'diamond_id': diamond_id,
            'quantity': int(self.quantity_var.get()),
            'unit_price': float(self.unit_price_var.get())
        }


class PurchaseDialog(BaseDialog):
    """نافذة حوار المشتريات"""

    def __init__(self, parent, supplier_repo, diamond_repo):
        self.supplier_repo = supplier_repo
        self.diamond_repo = diamond_repo
        self.items = []
        super().__init__(parent, "مشتريات جديدة")

    def create_widgets(self):
        """إنشاء عناصر نافذة المشتريات"""
        self.dialog.geometry("700x500")

        # إطار المحتوى
        content_frame = ttk.Frame(self.dialog, padding="10")
        content_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات المشتريات
        info_frame = ttk.LabelFrame(content_frame, text="معلومات المشتريات")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # المورد
        ttk.Label(info_frame, text="المورد:").grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
        self.supplier_var = tk.StringVar()
        supplier_combo = ttk.Combobox(info_frame, textvariable=self.supplier_var, width=30)

        # تحميل الموردين
        suppliers = self.supplier_repo.get_active_suppliers()
        supplier_values = [f"{s['SupplierID']} - {s['Name']}" for s in suppliers]
        supplier_combo['values'] = supplier_values
        supplier_combo.grid(row=0, column=1, pady=5, padx=5)

        # الملاحظات
        ttk.Label(info_frame, text="ملاحظات:").grid(row=1, column=0, sticky=tk.W, pady=5, padx=5)
        self.notes_var = tk.StringVar()
        ttk.Entry(info_frame, textvariable=self.notes_var, width=30).grid(row=1, column=1, pady=5, padx=5)

        # عناصر المشتريات
        items_frame = ttk.LabelFrame(content_frame, text="عناصر المشتريات")
        items_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # أزرار إضافة وحذف
        buttons_frame = ttk.Frame(items_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(buttons_frame, text="إضافة عنصر", command=self.add_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="حذف عنصر", command=self.remove_item).pack(side=tk.LEFT, padx=5)

        # جدول العناصر
        columns = ("الألماس", "الكمية", "سعر الوحدة", "الإجمالي")
        self.items_tree = ttk.Treeview(items_frame, columns=columns, show="headings", height=8)

        for col in columns:
            self.items_tree.heading(col, text=col)
            self.items_tree.column(col, width=120)

        self.items_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # الإجمالي
        total_frame = ttk.Frame(content_frame)
        total_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(total_frame, text="الإجمالي:", font=("Arial", 12, "bold")).pack(side=tk.RIGHT, padx=5)
        self.total_label = ttk.Label(total_frame, text="$0.00", font=("Arial", 12, "bold"))
        self.total_label.pack(side=tk.RIGHT, padx=5)

        # الأزرار
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.pack(fill=tk.X)

        ttk.Button(buttons_frame, text="حفظ", command=self.ok_clicked).pack(side=tk.RIGHT, padx=5)
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel_clicked).pack(side=tk.RIGHT, padx=5)

    def add_item(self):
        """إضافة عنصر للمشتريات"""
        dialog = PurchaseItemDialog(self.dialog, self.diamond_repo)
        if dialog.result:
            self.items.append(dialog.result)
            self.refresh_items()

    def remove_item(self):
        """حذف عنصر من المشتريات"""
        selection = self.items_tree.selection()
        if selection:
            item_index = self.items_tree.index(selection[0])
            del self.items[item_index]
            self.refresh_items()

    def refresh_items(self):
        """تحديث جدول العناصر"""
        # مسح الجدول
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة العناصر
        total = 0
        for item in self.items:
            diamond = self.diamond_repo.get_by_id(item['diamond_id'])
            diamond_name = f"{diamond['Carat']}ct {diamond['Color']} {diamond['Clarity']}" if diamond else "غير معروف"
            item_total = item['quantity'] * item['unit_price']
            total += item_total

            self.items_tree.insert("", "end", values=(
                diamond_name,
                item['quantity'],
                f"${item['unit_price']:,.2f}",
                f"${item_total:,.2f}"
            ))

        # تحديث الإجمالي
        self.total_label.config(text=f"${total:,.2f}")

    def validate(self):
        """التحقق من صحة بيانات المشتريات"""
        if not self.items:
            messagebox.showerror("خطأ", "يجب إضافة عنصر واحد على الأقل!")
            return False
        return True

    def get_data(self):
        """الحصول على بيانات المشتريات"""
        supplier_id = None
        if self.supplier_var.get():
            supplier_id = int(self.supplier_var.get().split(' - ')[0])

        return {
            'supplier_id': supplier_id,
            'notes': self.notes_var.get(),
            'items': self.items
        }


class PurchaseItemDialog(BaseDialog):
    """نافذة حوار عنصر المشتريات"""

    def __init__(self, parent, diamond_repo):
        self.diamond_repo = diamond_repo
        super().__init__(parent, "إضافة عنصر")

    def create_widgets(self):
        """إنشاء عناصر نافذة عنصر المشتريات"""
        self.dialog.geometry("400x250")

        # إطار المحتوى
        content_frame = ttk.Frame(self.dialog, padding="10")
        content_frame.pack(fill=tk.BOTH, expand=True)

        # الألماس
        ttk.Label(content_frame, text="الألماس:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.diamond_var = tk.StringVar()
        diamond_combo = ttk.Combobox(content_frame, textvariable=self.diamond_var, width=30)

        # تحميل الألماس
        diamonds = self.diamond_repo.get_active_diamonds()
        diamond_values = [f"{d['DiamondID']} - {d['Carat']}ct {d['Color']} {d['Clarity']}" for d in diamonds]
        diamond_combo['values'] = diamond_values
        diamond_combo.grid(row=0, column=1, pady=5, padx=(10, 0))

        # الكمية
        ttk.Label(content_frame, text="الكمية:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.quantity_var = tk.StringVar(value="1")
        ttk.Entry(content_frame, textvariable=self.quantity_var, width=30).grid(row=1, column=1, pady=5, padx=(10, 0))

        # سعر الوحدة
        ttk.Label(content_frame, text="سعر الوحدة ($):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.unit_price_var = tk.StringVar()
        ttk.Entry(content_frame, textvariable=self.unit_price_var, width=30).grid(row=2, column=1, pady=5, padx=(10, 0))

        # ربط تغيير الألماس بتحديث السعر
        diamond_combo.bind('<<ComboboxSelected>>', self.on_diamond_selected)

        # الأزرار
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=20)

        ttk.Button(buttons_frame, text="موافق", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)

    def on_diamond_selected(self, event):
        """عند اختيار ألماس"""
        if self.diamond_var.get():
            diamond_id = int(self.diamond_var.get().split(' - ')[0])
            diamond = self.diamond_repo.get_by_id(diamond_id)
            if diamond:
                self.unit_price_var.set(str(diamond.get('CostPrice', 0)))

    def validate(self):
        """التحقق من صحة بيانات العنصر"""
        try:
            if not self.diamond_var.get():
                messagebox.showerror("خطأ", "يرجى اختيار ألماس!")
                return False

            quantity = int(self.quantity_var.get())
            if quantity <= 0:
                messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر!")
                return False

            unit_price = float(self.unit_price_var.get())
            if unit_price <= 0:
                messagebox.showerror("خطأ", "سعر الوحدة يجب أن يكون أكبر من صفر!")
                return False

            return True
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة!")
            return False

    def get_data(self):
        """الحصول على بيانات العنصر"""
        diamond_id = int(self.diamond_var.get().split(' - ')[0])
        return {
            'diamond_id': diamond_id,
            'quantity': int(self.quantity_var.get()),
            'unit_price': float(self.unit_price_var.get())
        }

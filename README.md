# نظام إدارة مبيعات الألماس

نظام شامل لإدارة مبيعات الألماس مطور بلغة Python يدعم العمليات التالية:

## الميزات الرئيسية

1. **البيع والشراء**: تسجيل عمليات البيع والشراء للألماس مع تتبع كامل للمعاملات
2. **سندات القبض والصرف**: إدارة سندات القبض والصرف مع ربطها بالعملاء والموردين
3. **المخزون**: تتبع المخزون المتاح من الألماس مع إدارة الحجوزات والكميات
4. **النقدية**: إدارة التدفقات النقدية وتتبع الأرصدة
5. **القيود اليومية**: تسجيل القيود المحاسبية اليومية مع نظام محاسبة مزدوج القيد
6. **الدليل المحاسبي**: إدارة الحسابات المحاسبية مع هيكل هرمي
7. **الدعم متعدد العملات**: دعم عمليات البيع والشراء بعملات متعددة مع أسعار صرف ديناميكية

## هيكل المشروع

```
diamonds/
├── models/          # نماذج البيانات (Data Models)
│   └── base.py      # النماذج الأساسية لجميع الكيانات
├── database/        # طبقة قاعدة البيانات
│   ├── schema.py    # مخطط قاعدة البيانات
│   ├── base_repository.py  # المستودع الأساسي
│   └── repositories.py     # المستودعات المتخصصة
├── business/        # منطق الأعمال
│   ├── sales_manager.py      # مدير المبيعات
│   ├── purchase_manager.py   # مدير المشتريات
│   ├── inventory_manager.py  # مدير المخزون
│   ├── currency_manager.py   # مدير العملات
│   └── accounting_manager.py # مدير المحاسبة
├── ui/             # واجهة المستخدم
│   └── console_interface.py # واجهة سطر الأوامر
├── tests/          # الاختبارات
│   └── test_system.py       # اختبارات النظام
├── main.py         # نقطة البداية الرئيسية
├── requirements.txt # متطلبات المشروع
└── README.md       # هذا الملف
```

## التشغيل

### تشغيل النظام الرئيسي
```bash
python main.py
```

### تشغيل الاختبارات
```bash
python tests/test_system.py
```

## المتطلبات

- Python 3.8+
- SQLite3 (مدمج مع Python)
- جميع المكتبات المطلوبة مدمجة مع Python

## الميزات التقنية

### قاعدة البيانات
- استخدام SQLite لسهولة النشر والصيانة
- مخطط قاعدة بيانات محكم مع العلاقات والقيود
- دعم المعاملات (Transactions) لضمان سلامة البيانات
- فهرسة محسنة لتحسين الأداء

### معمارية النظام
- تطبيق نمط Repository Pattern لفصل طبقة البيانات
- فصل منطق الأعمال عن طبقة البيانات
- استخدام Data Classes لنمذجة البيانات
- معالجة شاملة للأخطاء

### إدارة العملات
- دعم عملات متعددة (دولار، ريال، يورو)
- أسعار صرف ديناميكية مع تاريخ
- تحويل تلقائي بين العملات
- دعم العملة الأساسية

### المحاسبة
- نظام محاسبة مزدوج القيد
- دليل محاسبي هرمي
- قيود يومية تلقائية للمعاملات
- ميزان مراجعة وتقارير مالية

## الاستخدام

### إضافة عميل جديد
1. اختر "إدارة العملاء" من القائمة الرئيسية
2. اختر "إضافة عميل جديد"
3. أدخل بيانات العميل

### إضافة ألماس جديد
1. اختر "إدارة الألماس والمخزون"
2. اختر "إضافة ألماس جديد"
3. أدخل مواصفات الألماس

### إنشاء مبيعات
1. اختر "المبيعات" من القائمة الرئيسية
2. اتبع الخطوات لإنشاء فاتورة مبيعات
3. أضف عناصر الألماس المطلوبة

## الاختبارات

النظام يتضمن مجموعة شاملة من الاختبارات التي تغطي:
- إنشاء قاعدة البيانات
- إضافة البيانات التجريبية
- اختبار عمليات المبيعات والمشتريات
- اختبار إدارة المخزون
- اختبار تحويل العملات

## المساهمة

هذا النظام مطور كنموذج أولي ويمكن تطويره وتحسينه حسب الحاجة.

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

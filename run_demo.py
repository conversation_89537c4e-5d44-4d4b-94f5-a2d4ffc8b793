#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لعرض النظام مع بيانات تجريبية
"""

import sys
import os
from datetime import date, datetime
from decimal import Decimal

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.schema import DatabaseSchema
from database.repositories import *
from business.sales_manager import SalesManager
from business.purchase_manager import PurchaseManager
from business.inventory_manager import InventoryManager
from business.currency_manager import CurrencyManager
from business.accounting_manager import AccountingManager


def setup_demo_data():
    """إعداد بيانات تجريبية للعرض"""
    print("إعداد نظام إدارة مبيعات الألماس...")
    print("=" * 50)
    
    # إنشاء قاعدة البيانات
    db_schema = DatabaseSchema("demo_diamonds.db")
    connection = db_schema.connect()
    
    # إنشاء الجداول
    db_schema.create_tables()
    db_schema.insert_default_data()
    print("✓ تم إنشاء قاعدة البيانات")
    
    # تهيئة المدراء
    sales_manager = SalesManager(connection)
    purchase_manager = PurchaseManager(connection)
    inventory_manager = InventoryManager(connection)
    currency_manager = CurrencyManager(connection)
    accounting_manager = AccountingManager(connection)
    
    # تهيئة المستودعات
    customer_repo = CustomerRepository(connection)
    supplier_repo = SupplierRepository(connection)
    diamond_repo = DiamondRepository(connection)
    
    # إضافة عملاء
    customer_data = {
        'Name': 'أحمد محمد السعودي',
        'ContactInfo': 'عميل مميز - تاجر مجوهرات',
        'Address': 'الرياض، المملكة العربية السعودية',
        'Email': '<EMAIL>',
        'Phone': '+************',
        'IsActive': True,
        'CreatedAt': datetime.now()
    }
    customer_id = customer_repo.insert(customer_data)
    print("✓ تم إضافة عميل تجريبي")
    
    # إضافة مورد
    supplier_data = {
        'Name': 'شركة الألماس الدولية',
        'ContactInfo': 'مورد رئيسي للألماس الفاخر',
        'Address': 'دبي، الإمارات العربية المتحدة',
        'Email': '<EMAIL>',
        'Phone': '+971501234567',
        'IsActive': True,
        'CreatedAt': datetime.now()
    }
    supplier_id = supplier_repo.insert(supplier_data)
    print("✓ تم إضافة مورد تجريبي")
    
    # إضافة أسعار صرف
    usd_currency = currency_manager.get_currency_by_code('USD')
    sar_currency = currency_manager.get_currency_by_code('SAR')
    
    if usd_currency and sar_currency:
        currency_manager.add_exchange_rate(
            usd_currency['CurrencyID'],
            sar_currency['CurrencyID'],
            Decimal('3.75'),
            date.today()
        )
        print("✓ تم إضافة أسعار الصرف")
    
    # إضافة ألماس فاخر
    diamonds_data = [
        {
            'Carat': Decimal('2.5'),
            'Color': 'D',
            'Clarity': 'FL',
            'Cut': 'Excellent',
            'Shape': 'Round',
            'CertificateNumber': 'GIA-2024-001',
            'CostPrice': Decimal('15000'),
            'SellingPrice': Decimal('25000'),
            'CurrencyID': usd_currency['CurrencyID'] if usd_currency else 1,
            'Description': 'ألماس دائري فاخر عالي الجودة',
            'IsActive': True,
            'CreatedAt': datetime.now()
        },
        {
            'Carat': Decimal('1.8'),
            'Color': 'E',
            'Clarity': 'VVS1',
            'Cut': 'Excellent',
            'Shape': 'Princess',
            'CertificateNumber': 'GIA-2024-002',
            'CostPrice': Decimal('8000'),
            'SellingPrice': Decimal('14000'),
            'CurrencyID': usd_currency['CurrencyID'] if usd_currency else 1,
            'Description': 'ألماس أميرة ممتاز',
            'IsActive': True,
            'CreatedAt': datetime.now()
        }
    ]
    
    diamond_ids = []
    for diamond_data in diamonds_data:
        diamond_id = diamond_repo.insert(diamond_data)
        diamond_ids.append(diamond_id)
        
        # إضافة مخزون
        inventory_data = {
            'DiamondID': diamond_id,
            'Quantity': 5,
            'ReservedQuantity': 0,
            'LastUpdated': datetime.now()
        }
        inventory_repo = InventoryRepository(connection)
        inventory_repo.insert(inventory_data)
    
    print("✓ تم إضافة ألماس فاخر للمخزون")
    
    # إنشاء مبيعات تجريبية
    sale_id = sales_manager.create_sale(
        customer_id=customer_id,
        sale_date=date.today(),
        currency_id=usd_currency['CurrencyID'] if usd_currency else 1,
        notes="مبيعات تجريبية - عرض النظام"
    )
    
    # إضافة عناصر للمبيعات
    sales_manager.add_sale_item(
        sale_id=sale_id,
        diamond_id=diamond_ids[0],
        quantity=1,
        unit_price=Decimal('25000')
    )
    
    # إنهاء المبيعات
    sales_manager.finalize_sale(sale_id)
    print("✓ تم إنشاء مبيعات تجريبية")
    
    # إنشاء مشتريات تجريبية
    purchase_id = purchase_manager.create_purchase(
        supplier_id=supplier_id,
        purchase_date=date.today(),
        currency_id=usd_currency['CurrencyID'] if usd_currency else 1,
        notes="مشتريات تجريبية - تجديد المخزون"
    )
    
    # إضافة عناصر للمشتريات
    purchase_manager.add_purchase_item(
        purchase_id=purchase_id,
        diamond_id=diamond_ids[1],
        quantity=3,
        unit_price=Decimal('8000')
    )
    
    # إنهاء المشتريات
    purchase_manager.finalize_purchase(purchase_id)
    print("✓ تم إنشاء مشتريات تجريبية")
    
    # عرض ملخص النظام
    print("\n" + "=" * 50)
    print("ملخص النظام:")
    print("=" * 50)
    
    # عرض المخزون
    available_inventory = inventory_manager.get_available_inventory()
    print(f"📦 عدد قطع الألماس في المخزون: {len(available_inventory)}")
    
    # عرض قيمة المخزون
    inventory_value = inventory_manager.get_inventory_value()
    print(f"💎 قيمة المخزون الإجمالية: ${inventory_value['total_selling_value']:,.2f}")
    
    # عرض أرصدة النقدية
    if usd_currency:
        usd_balance = CashRepository(connection).get_cash_balance_by_currency(usd_currency['CurrencyID'])
        print(f"💰 الرصيد النقدي (دولار): ${usd_balance:,.2f}")
    
    if sar_currency:
        sar_balance = CashRepository(connection).get_cash_balance_by_currency(sar_currency['CurrencyID'])
        print(f"💰 الرصيد النقدي (ريال): {sar_balance:,.2f} ر.س")
    
    # عرض العملاء والموردين
    customers = customer_repo.get_active_customers()
    suppliers = supplier_repo.get_active_suppliers()
    print(f"👥 عدد العملاء النشطين: {len(customers)}")
    print(f"🏢 عدد الموردين النشطين: {len(suppliers)}")
    
    print("\n" + "=" * 50)
    print("تم إعداد النظام بنجاح! يمكنك الآن تشغيل:")
    print("python main.py")
    print("للوصول إلى الواجهة التفاعلية")
    print("=" * 50)
    
    # إغلاق الاتصال
    db_schema.close()


if __name__ == "__main__":
    setup_demo_data()
